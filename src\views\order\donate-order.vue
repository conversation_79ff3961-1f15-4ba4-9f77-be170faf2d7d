<template>
  <div class="donate-order-container">
    <div class="container-header">
      <div class="left">
        <div class="filter-row">
          <el-input
            v-model="queryParams.search"
            placeholder="关键字"
            size="large"
          />
        </div>
        <div class="btn primary-btn" @click="handleQuery">
          <i-ep-search /> 搜索
        </div>
        <div class="filter-row">
          <el-select
            :suffix-icon="`CaretBottom`"
            size="large"
            v-model="queryParams.type"
            placeholder="赠予类型"
            filterable
            clearable
            @change="handleQuery"
          >
            <el-option
              v-for="item in donateOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </div>
        <div class="filter-row">
          <el-select
            :suffix-icon="`CaretBottom`"
            size="large"
            v-model="queryParams.status"
            placeholder="订单状态"
            filterable
            clearable
            @change="handleQuery"
          >
            <el-option
              v-for="item in statusOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </div>
        <!-- <div class="filter-row">
          <el-date-picker
            class="date-picker"
            size="large"
            v-model="queryParams.dateTimeRange"
            type="daterange"
            range-separator="~"
            start-placeholder="开始时间"
            end-placeholder="结束时间"
            value-format="YYYY-MM-DD"
            @change="handleQuery"
          />
        </div> -->
      </div>
      <!-- <div class="right"></div> -->
    </div>
    <div class="content">
      <el-table
        v-loading="loading"
        element-loading-text="Loading"
        element-loading-background="#ffffffb4"
        :data="tableData"
        height="100%"
        border
        fit
        highlight-current-row
      >
        <el-table-column label="用户名" align="center" min-width="100">
          <template #default="scope">
            {{ scope.row.receiver.name || "绿知学员" + scope.row.receiver.id }}
          </template>
        </el-table-column>
        <el-table-column label="手机号码" align="center" min-width="120">
          <template #default="scope">
            {{ scope.row.receiver.mobile }}
          </template>
        </el-table-column>
        <el-table-column label="赠予类型" align="center" min-width="40">
          <template #default="scope">
            <span :style="{ color: donateTypeMap[scope.row.type]?.color }">
              {{ donateTypeMap[scope.row.type]?.label }}
            </span>
            <!-- <el-tag :type="donateTypeMap[scope.row.type]?.type">{{
              donateTypeMap[scope.row.type]?.label
            }}</el-tag> -->
          </template>
        </el-table-column>
        <el-table-column label="有效期" align="center" min-width="80">
          <template #default="scope">
            {{ scope.row.expired_at || "--" }}
          </template>
        </el-table-column>
        <el-table-column
          label="操作时间"
          align="center"
          min-width="100"
          show-overflow-tooltip
        >
          <template #default="scope">
            {{ scope.row.created_at }}
          </template>
        </el-table-column>
        <el-table-column label="订单状态" align="center" min-width="50">
          <template #default="scope">
            <el-tag :type="statusMap[scope.row.status]?.type">{{
              statusMap[scope.row.status]?.label
            }}</el-tag>
          </template>
        </el-table-column>

        <el-table-column label="操作" align="center" min-width="80">
          <template #default="scope">
            <div class="option-btn">
              <div
                class="btn primary-btn"
                @click="onRowClick('detail', scope.row)"
              >
                清单
              </div>
              <div
                v-if="!scope.row.is_expired && scope.row.status == 10"
                class="btn info-btn"
                @click="onRowClick('revocation', scope.row)"
              >
                撤销
              </div>
            </div>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <div class="footer">
      <pagination
        v-if="total > 0"
        v-model:total="total"
        v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize"
        @pagination="getData"
      />
    </div>

    <!-- 清单、撤销弹窗 -->
    <el-dialog
      class="list-dialog"
      v-model="listDialog.visible"
      :title="listDialog.title"
      :width="listDialog.width"
      append-to-body
      @close="closeListDialog"
    >
      <div class="dialog-body">
        <div class="order-desc">
          <div class="avatar-img">
            <img
              :src="orderDetail.receiver?.avatar || userStore.defaultAvatar"
              alt=""
            />
          </div>
          <div class="desc">
            <div class="name">
              {{
                orderDetail.receiver?.name ||
                "绿知学员" + orderDetail.receiver?.id
              }}
            </div>
            <div class="mobile">
              <span>{{ orderDetail.receiver?.mobile }}</span>
            </div>
          </div>
        </div>
        <div class="order-content">
          <template v-if="orderDetail.type == 20">
            <div class="donate-bean">
              <span
                >赠予数量：{{
                  orderDetail.content[0]?.bean_amount || "--"
                }}</span
              >
            </div>
          </template>
          <template v-else-if="orderDetail.type == 30">
            <div class="donate-bean">
              <span>赠予VIP有效期至：{{ orderDetail.expired_at || "--" }}</span>
            </div>
          </template>
          <template v-else>
            <div class="donate-course">
              <el-table
                :data="orderDetail.courseList"
                height="100%"
                border
                fit
                highlight-current-row
                ref="courseRef"
              >
                <el-table-column label="名称" align="center" min-width="140">
                  <template #default="scope">
                    <div class="cover-name">
                      <img :src="scope.row.thumb" alt="" class="cover-img" />
                      <div class="cover-text">
                        <span>{{ scope.row.title }}</span>
                        <span
                          v-if="scope.row.has_chapter"
                          class="composite-course"
                          @click="handleCompositeCourse(scope.row)"
                        >
                          套课
                          <i-ep-caret-bottom />
                        </span>
                      </div>
                    </div>
                  </template>
                </el-table-column>
                <el-table-column
                  label="所属分类"
                  align="center"
                  min-width="80"
                  show-overflow-tooltip
                >
                  <template #default="scope">
                    {{ filterCategoriesName(scope.row.categories) }}
                  </template>
                </el-table-column>
              </el-table>
            </div>
          </template>
          <span class="tips" v-if="listDialog.type == 'revocation'"
            >撤销后无法恢复，请确认是否撤销赠予？</span
          >
        </div>
      </div>
      <template #footer>
        <div class="dialog-footer" v-if="listDialog.type == 'detail'">
          <div class="btn primary-btn" @click="closeListDialog">确 定</div>
        </div>
        <div class="dialog-footer" v-if="listDialog.type == 'revocation'">
          <div class="btn cancel-btn" @click="closeListDialog">取 消</div>
          <div class="btn primary-btn" @click="submitRevocation">确 定</div>
        </div>
      </template>
    </el-dialog>

    <!-- 套课弹窗 -->
    <el-dialog
      class="composite-course-dialog"
      v-model="compositeCourseDialog.visible"
      :title="compositeCourseDialog.title"
      :width="compositeCourseDialog.width"
      append-to-body
      @close="closeCompositeCourseDialog"
    >
      <template #header>
        <div class="dialog-header">
          <div class="course-desc">
            <div class="course-img">
              <img :src="compositeCourseDetail.thumb" alt="" />
            </div>
            <div class="desc">
              <div class="title">{{ compositeCourseDetail.title }}</div>
              <div class="count">
                <svg-icon class="icon-img icon-book" icon-class="book" />
                课程：{{ totalCourse(compositeCourseDetail) }}
              </div>
              <div class="time">
                <svg-icon class="icon-img icon-clock" icon-class="clock" />
                时长：{{
                  secondsToHoursAndMinutes(compositeCourseDetail.duration || 0)
                }}
              </div>
            </div>
          </div>
        </div>
      </template>
      <div class="dialog-body">
        <div class="course-menus">
          <el-scrollbar
            style="width: 100%; height: 100%"
            warp-style="overflow-x: hidden;"
          >
            <el-tree
              class="dialog-tree course-menus-tree disabled-tree"
              :props="compositeCourseProps"
              ref="compositeCourseTreeRef"
              :data="compositeCourseTree"
              :default-expand-all="true"
              highlight-current
              node-key="id"
              show-checkbox
              :default-checked-keys="compositeCourseFrom.lessons_id"
            />
          </el-scrollbar>
        </div>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <div class="left"></div>
          <div class="right">
            <div class="btn primary-btn" @click="closeCompositeCourseDialog">
              确 定
            </div>
          </div>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { useAppStore } from "@/store/modules/app";
import { useRoute, useRouter } from "vue-router";
import { getGiveOrders, cancelGiveOrder } from "@/api/order";
import {
  parseTime,
  secondsToHoursAndMinutes,
  resetReactiveObject,
  formatStringDate,
} from "@/utils";
import { useUserStore } from "@/store/modules/user";
import { getCourses, getCoursesDetail } from "@/api/course";
const { proxy } = getCurrentInstance() as any;
// proxy 替代 this
defineOptions({
  name: "DonateOrder",
  inheritAttrs: false,
});
/** 仓库*/
const store = useAppStore();
/**路由对象*/
const route = useRoute();
/**路由实例*/
const router = useRouter();
const userStore = useUserStore();

const sortOptions = reactive<any>([]);
const loading = ref(false);
// const removeIds = ref([]); // 删除用户ID集合 用于批量删除
const queryParams = reactive<any>({
  pageNum: 1,
  pageSize: 20,
  type: "",
  status: "",
  search: "",
});
const donateOptions = ref<any>([
  { value: 10, label: "课程" },
  {
    value: 20,
    label: "绿豆",
  },
  {
    value: 30,
    label: "会员",
  },
]);
const donateTypeMap = reactive<any>({
  10: { type: "primary", label: "课程", color: "#409eff" },
  20: { type: "success", label: "绿豆", color: "#2ab7b0" },
  30: { type: "warning", label: "会员", color: "#e6a23c" },
  // 50: { type: "info", label: "xx", color: "#909399" },
  // 40: { type: "danger", label: "xx", color: "#f56c6c" },
});

const statusOptions = ref<any>([
  { value: 10, label: "正常" },
  { value: 20, label: "撤销" },
]);
const statusMap = reactive<any>({
  10: { type: "success", label: "正常" },
  20: { type: "info", label: "撤销" },

  // add more status mappings as needed
});

const total = ref<any>(0); // 数据总数
const tableData = ref<any>([]);
const bean_img = ref(new URL("@/assets/images/bean.png", import.meta.url).href);
const default_img = new URL("@/assets/images/cover.jpg", import.meta.url).href;
// 清单弹窗
const listDialog = reactive<any>({
  visible: false,
  type: "bean", //course
  width: "35%",
  title: "赠予详情",
});
const revocationDialog = reactive<any>({
  visible: false,
  type: "bean", //course
  width: "35%",
  title: "撤销订单",
});

const courseRef = ref<any>(null);
const courseDonateForm = reactive<any>({
  expired_at: "",
  courses: [],
  receiver_id: "",
});
const compositeCourseDialog = reactive<any>({
  visible: false,
  type: "compositeCourse",
  width: "30%",
  title: "套课",
});
const compositeCourseTreeRef = ref<any>(null);
const compositeCourseDetail = reactive<any>({}); // 课程详情
const compositeCourseFrom = reactive<any>({}); // 套课表单
const compositeCourseTree = ref<any>([]); // 套课数据
const compositeCourseProps = reactive<any>({
  //自定义label
  label: (data: { label: any }) => {
    return data.label; // name为你要显示的名称 可以自定义，就是将name替换label
  },
  children: "children",
});

const rowId = ref<any>(null);
const orderDetail = reactive<any>({});
const listDialogLoading = ref<any>(false);

watch(
  () => queryParams.dateTimeRange,
  (newVal) => {
    if (newVal) {
      queryParams.from = parseTime(newVal[0], "{y}{m}{d}000000");
      queryParams.to = parseTime(newVal[1], "{y}{m}{d}235959");
    }
  },
  { deep: true }
);
onMounted(() => {
  getData();
});

function getData() {
  loading.value = true;
  const params = {
    search: queryParams.search || undefined,
    page: queryParams.pageNum,
    per_page: queryParams.pageSize,
    from: queryParams.from || undefined,
    to: queryParams.to || undefined,
    type: queryParams.type || undefined,
    status: queryParams.status || undefined,
  };
  if (!queryParams.dateTimeRange) {
    delete params.from;
    delete params.to;
  }
  getGiveOrders(params).then((res: any) => {
    if (res.status == 200) {
      tableData.value = res.data.orders.map((item: any) => {
        item.is_expired = false;
        if (item.type == 10) {
          const exp_time = new Date(item.expired_at).getTime();
          const now_time = new Date().getTime();
          if (exp_time < now_time) {
            item.is_expired = true;
          }
        } //过期判断
        item.created_at = parseTime(item.created_at, "{y}-{m}-{d} {h}:{i}:{s}");
        item.expired_at = item.expired_at
          ? parseTime(item.expired_at, "{y}-{m}-{d} {h}:{i}:{s}")
          : "永久有效";
        return item;
      });
      total.value = res.total;
      loading.value = false;
    }
  });
}
function handleQuery() {
  queryParams.pageNum = 1;
  getData();
}
function filterCoursesById(content: any[], id: any) {
  return content.filter((item: any) => item.id === id);
}
function onRowClick(type: string, row: any) {
  rowId.value = row.id;
  Object.assign(orderDetail, row, {
    content: JSON.parse(row.content),
  });
  if (row.type == 10) {
    nextTick(() => {
      getCourses({ page: 1, per_page: 9999 }).then((res: any) => {
        const courses = res.data.courses;
        // 合并 orderDetail.content 和 courses
        const merged = courses
          .map((course: any) => {
            const matchedContent = orderDetail.content.find(
              (item: any) => item.course_id == course.id
            );
            const res = { ...course, ...matchedContent };
            return res;
          })
          .filter((item: any) => item.lesson_ids);
        Object.assign(orderDetail, { courseList: merged });
      });
    });
  }
  if (row.type == 30) {
    // if (orderDetail.content && orderDetail.content[0]) {
    //   const contentItem = orderDetail.content[0];
    //   if (contentItem.from) {
    //     contentItem.from = formatStringDate(contentItem.from, "{y}-{m}-{d} ");
    //   }
    //   if (contentItem.till) {
    //     contentItem.till = formatStringDate(contentItem.till, "{y}-{m}-{d} ");
    //   }
    // }
    orderDetail.expired_at = parseTime(orderDetail.expired_at, "{y}-{m}-{d} ");
  }

  if (type === "detail") {
    listDialog.title = "订单详情";
    listDialog.type = "detail";
  }
  if (type === "revocation") {
    listDialog.type = "revocation";
    listDialog.title = "撤销订单";
  }
  setTimeout(() => {
    listDialog.visible = true;
  }, 300);
}

// 套课详情弹窗
function getAllNodeKeys(treeData: any[]) {
  const keys: any[] = [];
  function traverse(nodes: any[]) {
    nodes.forEach((node) => {
      keys.push(node.id);
      if (node.children && node.children.length > 0) {
        traverse(node.children);
      }
    });
  }
  traverse(treeData);
  return keys;
} //获取所有node
function handleCompositeCourse(row: any) {
  console.log("orderDetail.courseList", orderDetail.courseList);
  compositeCourseDetail.id = row.id;
  compositeCourseDialog.visible = true;
  getCoursesDetail(row.id).then((res: any) => {
    Object.assign(compositeCourseDetail, res.data);
    compositeCourseDetail.title = row.title;
    if (res.data.chapters && res.data.chapters.length > 0) {
      compositeCourseTree.value = res.data.chapters.map((item: any) => {
        let res = {
          id: item.id + item.title + "",
          label: item.title,
          type: "chapter",
          children: item.videos.map((itm: any) => {
            return {
              id: itm.id,
              label: itm.name,
            };
          }),
        };
        return res;
      });
    }

    // 确保树数据加载完成后设置选中项
    nextTick(() => {
      if (row.lesson_ids == "all") {
        // 全选
        // compositeCourseTree.value.forEach((node: any) => {
        //   proxy?.$refs.compositeCourseTreeRef.setChecked(node.id, true);
        // });
        const allKeys = getAllNodeKeys(compositeCourseTree.value);
        proxy?.$refs.compositeCourseTreeRef.setCheckedKeys(allKeys, true);
      } else {
        const lesson_ids = row.lesson_ids.split(",");
        lesson_ids.forEach((item: any) => {
          const node = proxy?.$refs.compositeCourseTreeRef.getNode(item);
          if (node) {
            proxy?.$refs.compositeCourseTreeRef.setChecked(node, true);
          } else {
            console.error(` ${item}`);
          }
        });
      }
    });
  });
}
function filterCategoriesName(value: any) {
  let res = "--";
  res = value?.map((item: any) => item.name).join(",");
  return res;
}
function filterSectionsName(value: any) {
  let res = "--";
  res = value?.map((item: any) => item.name).join(",");
  return res;
}
function closeListDialog() {
  listDialog.visible = false;
  resetReactiveObject(orderDetail);
}
function totalCourse(val) {
  let res = 0;
  let chapter = 0;
  let video = 0;
  const courseChapters = val.chapters;
  const videoList = val.videos;
  if (courseChapters?.length > 0) {
    courseChapters.forEach((item) => {
      chapter++;
      if (item.videos.length) {
        item.videos.forEach((itm) => {
          // res++;
          video++;
        });
      }
    });
  } else if (videoList?.length > 0) {
    videoList.forEach((item) => {
      // res++;
      video++;
    });
  }
  return chapter != 0 ? chapter + "章" + video + "节" : video + "课时";
}
function closeCompositeCourseDialog() {
  compositeCourseDialog.visible = false;
  compositeCourseTree.value = [];
  resetReactiveObject(compositeCourseDetail);
  resetReactiveObject(compositeCourseFrom);
}

//撤销订单弹窗
function submitRevocation() {
  cancelGiveOrder(rowId.value)
    .then((res: any) => {
      if (res.status == 200) {
        ElMessage.success("撤销成功");
        closeListDialog();
        getData();
      }
    })
    .catch((err: any) => {
      //ElMessage.error("撤销失败");
    });
}
function closeRevocationDialog() {
  revocationDialog.visible = false;
}
</script>

<style scoped lang="scss">
.donate-order-container {
  // width: 50%;
  height: 95%;
  margin: 20px;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 3px 20px 1px rgb(109 125 126 / 7%);

  .container-header {
    display: flex;
    justify-content: space-between;
    width: 100%;
    padding: 20px;

    .left {
      display: flex;
      align-items: center;
      justify-content: space-between;
      // width: 60%;
      .btn {
        width: 116px;
        height: 40px;
        margin-left: 20px;
      }
    }

    .filter-row {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 260px;
      margin-left: 20px;

      &:nth-child(1) {
        width: 260px;
        margin-left: 0;
      }

      .btn {
        width: 116px;
        height: 40px;
        margin-left: 20px;
      }
    }
  }

  .content {
    width: 100%;
    height: calc(100% - 170px);
    padding: 10px 20px;
  }

  .footer {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-top: 15px;
  }
}

.list-dialog {
  padding: 20px 10px;
}

.dialog-body {
  .order-desc {
    display: flex;
    align-items: center;
    width: 100%;
    padding: 10px;
    margin-bottom: 20px;
    background: #a6f0b03e;

    .avatar-img {
      width: 100px;
      height: 100px;
      overflow: hidden;
      border-radius: 50%;

      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }
    }

    .desc {
      display: flex;
      flex-direction: column;
      // align-items: center;
      justify-content: center;
      margin-left: 20px;

      .name {
        font-size: 20px;
        font-weight: bold;
        color: #333;
      }

      .mobile {
        margin-top: 10px;
        font-size: 18px;
        color: #666;

        span {
          color: #999;
        }
      }
    }
  }

  .order-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    width: 100%;

    .donate-bean {
      padding: 20px 0;
      font-size: 24px;
      font-weight: 600px;
    }

    .donate-course {
      width: 100%;
      height: 40vh;

      .cover-name {
        display: flex;
        align-items: center;

        .cover-img {
          // width: 150px !important;
          // height: 100px !important;
          width: 162px !important;
          height: 86px !important;
          object-fit: cover;
          border-radius: 8px;
        }

        .cover-text {
          display: flex;
          flex-direction: column;
          align-items: center;
          width: 40%;
        }
      }

      .composite-course {
        margin-top: 5px;
        font-size: 15px;
        color: #00918c;
        cursor: pointer;
      }
    }
  }

  .tips {
    margin-top: 10px;
    font-size: 15px;
    color: red !important;
  }
}
</style>
<style lang="scss">
.composite-course-dialog {
  padding: 0 !important;

  .el-dialog__header,
  .el-dialog__header.show-close {
    padding: 0 !important;
    margin-bottom: 0 !important;
  }

  .el-dialog__body {
    padding: 0 1rem !important;
  }

  .dialog-header {
    display: flex;
    align-items: center;
    width: 100%;
    height: 216px;
    padding-left: 50px;
    background: linear-gradient(180deg, #eaf7ec 0%, #fff 100%);
    background-size: 100% 100%;
    border-radius: 8px 8px 0 0;

    .course-desc {
      display: flex;
      align-items: center;

      .course-img {
        width: 243px;
        height: 129px;
        overflow: hidden;
        border-radius: 16px;

        img {
          width: 100%;
          height: 100%;
          object-fit: cover;
        }
      }

      .desc {
        margin-left: 20px;
        text-align: left;

        .title {
          margin-bottom: 30px;
          font-size: 23px;
          font-weight: 500;
          color: #3b4664;
        }

        .count,
        .time {
          font-size: 18px;
          font-weight: 400;
          color: #3d4040;
        }
      }
    }
  }

  .dialog-body {
    display: flex;
    align-items: center;
    justify-content: center;

    .course-menus {
      display: flex;
      width: 90%;
      height: 40vh;
      border: 1px solid #edeff4;
      border-radius: 8px;
      box-shadow: inset 3px 3px 6px 1px rgb(188 198 214 / 14%);
    }

    .course-menus-tree {
      width: 100%;
    }

    .disabled-tree {
      pointer-events: none;
    }
  }

  .dialog-footer {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    padding: 10px 20px 30px;

    .left {
      display: flex;
      text-align: left;

      span {
        padding: 2px 0;
        font-size: 20px;
        font-weight: 500;
        color: #00918c;
      }
    }

    .right {
      display: flex;
      align-items: center;
    }
  }
}
</style>
