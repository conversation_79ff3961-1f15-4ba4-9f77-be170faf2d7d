<template>
  <div class="question-bank-container">
    <div class="left-sidebar">
      <!-- 题库主题列表 -->
      <el-scrollbar
        style="width: 100%; height: 90%"
        warp-style="overflow-x: hidden;"
      >
        <div class="sidebar-list">
          <draggable
            v-model="sidebarTopics"
            class="drag-container"
            :item-key="'topic'"
            @end="endVideosMove"
            :handle="'.sort'"
          >
            <template #item="{ element: item, index: idx }">
              <div
                class="green-item"
                :key="idx"
                @click="handleTopicClick('active', item)"
                :class="[item.id === activeTopic ? 'active-item' : '']"
              >
                <div class="green-title">
                  <div class="left">
                    <div class="icon sort">
                      <svg-icon icon-class="drag3" v-if="idx !== 0" />
                    </div>
                    <span class="text">{{ item.name }} </span>
                  </div>

                  <div class="right" v-if="idx !== 0">
                    <div
                      class="icon edit"
                      @click.stop="handleTopicClick('edit', item)"
                    >
                      <svg-icon icon-class="edit2" />
                    </div>
                    <div
                      class="icon delete"
                      @click.stop="handleTopicClick('delete', item)"
                    >
                      <svg-icon icon-class="delete" />
                    </div>
                  </div>
                </div>
              </div>
            </template>
          </draggable>
        </div>
      </el-scrollbar>

      <div
        class="sidebar-footer"
        style="position: absolute; bottom: 20px; width: 90%"
      >
        <!-- <div class="btn text-btn" @click="handleTopicClick('edit')">编辑</div> -->
        <div class="btn text-btn" @click="handleTopicClick('create')">
          新增主题
        </div>
        <!-- <div class="btn text-btn" @click="handleTopicClick('save')">保存</div> -->
      </div>
    </div>
    <div class="right-content">
      <div class="container-header">
        <div class="left">
          <div class="filter-row">
            <el-input
              v-model="queryParams.search"
              placeholder="请输入关键字"
              clearable
              size="large"
            />
          </div>
          <div class="btn primary-btn" @click="handleQuery">
            <i-ep-search /> 搜索
          </div>
        </div>
        <div class="right">
          <div class="text-btn" @click="handleCreate">
            <i-ep-circle-plus style="margin-right: 5px" />
            新增题库
          </div>
        </div>
      </div>

      <div class="content">
        <el-table
          v-loading="loading"
          element-loading-text="Loading"
          element-loading-background="#ffffffb4"
          :data="tableData"
          height="100%"
          border
          fit
          highlight-current-row
        >
          <el-table-column label="题库名称" align="center" min-width="120">
            <template #default="scope">
              {{ scope.row.name }}
            </template>
          </el-table-column>
          <!-- <el-table-column label="关联课程" align="center" min-width="100">
            <template #default="scope">
              <div class="course-tags">
                <el-tag
                  v-for="course in scope.row.courses"
                  :key="course.id"
                  size="small"
                  type="success"
                >
                  {{ course.name }}
                </el-tag>
              </div>
            </template>
          </el-table-column> -->

          <el-table-column label="题目数量" align="center" min-width="60">
            <template #default="scope">
              <span style="color: #00918c"> {{ scope.row.total_q }}</span>
            </template>
          </el-table-column>
          <!-- <el-table-column label="试题类型" align="center" min-width="100">
          <template #default="scope">
            {{ getQuestionTypeText(scope.row.questionType) }}
          </template>
        </el-table-column> -->

          <el-table-column label="创建人" align="center" min-width="60">
            <template #default="scope">
              {{ scope.row.creator }}
            </template>
          </el-table-column>

          <el-table-column label="创建时间" align="center" min-width="120">
            <template #default="scope">
              {{ scope.row.created_at }}
            </template>
          </el-table-column>

          <el-table-column label="状态" align="center" min-width="80">
            <template #default="scope">
              <el-tag :type="scope.row.enabled === true ? 'success' : 'danger'">
                {{ scope.row.enabled === true ? "启用" : "停用" }}
              </el-tag>
            </template>
          </el-table-column>

          <el-table-column label="操作" align="center" min-width="200">
            <template #default="scope">
              <div class="option-btn">
                <div
                  class="btn deep-blue-btn"
                  @click="onRowClick('link', scope.row)"
                >
                  关联课程
                </div>

                <div
                  class="btn light-blue-btn"
                  @click="onRowClick('detail', scope.row)"
                >
                  详情
                </div>
                <div
                  class="btn primary-btn"
                  @click="onRowClick('edit', scope.row)"
                >
                  修改
                </div>
                <div
                  class="btn"
                  :class="[
                    scope.row.enabled === false
                      ? 'light-green-btn'
                      : 'info-btn',
                  ]"
                  @click="onRowClick('status', scope.row)"
                >
                  {{ scope.row.enabled === false ? "启用" : "停用" }}
                </div>
                <div
                  class="btn delete-btn"
                  @click="onRowClick('delete', scope.row)"
                >
                  删除
                </div>
              </div>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <div class="footer">
        <pagination
          v-if="total > 0"
          v-model:total="total"
          v-model:page="queryParams.pageNum"
          v-model:limit="queryParams.pageSize"
          @pagination="getData"
        />
      </div>
    </div>

    <!-- 关联课程弹窗 -->
    <el-dialog
      class="link-course-dialog"
      v-model="linkCourseDialog.visible"
      :title="linkCourseDialog.title"
      :width="linkCourseDialog.width"
      :before-close="closeLinkCourseDialog"
      append-to-body
    >
      <div
        class="link-course-content"
        v-loading="linkLoading"
        element-loading-text="Loading"
        element-loading-background="#ffffffb4"
      >
        <div
          class="course-selection custom-checkbox-rectangle checkbox-rectangle-green"
        >
          <!-- 左侧：未关联课程 -->
          <div class="course-list">
            <div class="list-header unlinked">
              <span class="header-title">未关联课程</span>
            </div>
            <div class="search-box">
              <div class="select-all-box">
                <el-checkbox
                  :model-value="isAllUnlinkedSelected"
                  :indeterminate="isUnlinkedIndeterminate"
                  @change="handleSelectAllUnlinked"
                >
                  全选
                </el-checkbox>
              </div>
              <el-input
                v-model="linkCourseForm.leftSearch"
                placeholder="请输入名称"
                clearable
                size="large"
              >
                <template #suffix>
                  <el-icon><i-ep-search /></el-icon>
                </template>
              </el-input>
            </div>
            <div class="course-items">
              <div
                v-for="course in filteredUnlinkedCourses"
                :key="course.id"
                class="course-item"
                @click="toggleCourseSelection(course, 'unlinked')"
              >
                <el-checkbox
                  :model-value="
                    linkCourseForm.selectedUnlinkedIds.includes(course.id)
                  "
                  @change="
                    (checked) =>
                      handleCheckboxChange(course, 'unlinked', checked)
                  "
                  @click.stop
                >
                  {{ course.name }}
                </el-checkbox>
              </div>
            </div>
          </div>

          <!-- 中间：操作按钮 -->
          <div class="operation-buttons">
            <div class="btn-group">
              <div
                class="right-btn"
                @click="addCourseLink"
                :class="{
                  'disabled-btn':
                    linkCourseForm.selectedUnlinkedIds.length === 0,
                }"
              >
                <!-- → -->
              </div>

              <div
                class="left-btn"
                @click="removeCourseLink"
                :class="{
                  'disabled-btn': linkCourseForm.selectedLinkedIds.length === 0,
                }"
              >
                <!-- ← -->
              </div>
            </div>
          </div>

          <!-- 右侧：已关联课程 -->
          <div class="course-list">
            <div class="list-header linked">
              <span class="header-title">已关联课程</span>
            </div>
            <div class="search-box">
              <div class="select-all-box">
                <el-checkbox
                  :model-value="isAllLinkedSelected"
                  :indeterminate="isLinkedIndeterminate"
                  @change="handleSelectAllLinked"
                >
                  全选
                </el-checkbox>
              </div>
              <el-input
                v-model="linkCourseForm.rightSearch"
                placeholder="请输入名称"
                clearable
                size="large"
              >
                <template #suffix>
                  <el-icon><i-ep-search /></el-icon>
                </template>
              </el-input>
            </div>
            <div class="course-items">
              <div
                v-for="course in filteredLinkedCourses"
                :key="course.id"
                class="course-item"
                @click="toggleCourseSelection(course, 'linked')"
              >
                <el-checkbox
                  :model-value="
                    linkCourseForm.selectedLinkedIds.includes(course.id)
                  "
                  @change="
                    (checked) => handleCheckboxChange(course, 'linked', checked)
                  "
                  @click.stop
                >
                  {{ course.name }}
                </el-checkbox>
              </div>
            </div>
          </div>
        </div>

        <!-- 底部提示 -->
        <div class="tips">
          <span class="tip-text"
            >注：一个题库可关联多个课程；一个课程只能关联一个题库</span
          >
        </div>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <div class="btn cancel-btn" @click="closeLinkCourseDialog">取 消</div>
          <div class="btn primary-btn" @click="saveLinkCourse">保 存</div>
        </div>
      </template>
    </el-dialog>

    <!-- 主题弹窗 -->
    <el-dialog
      v-model="topicDialog.visible"
      :title="topicDialog.title"
      :width="topicDialog.width"
      :before-close="closeTopicDialog"
      append-to-body
    >
      <div class="dialog-body">
        <el-form :model="topicForm" ref="topicFormRef">
          <el-form-item
            label="主题名称"
            prop="name"
            :rules="[
              { required: true, message: '请输入主题名称', trigger: 'blur' },
            ]"
          >
            <el-input
              v-model="topicForm.name"
              placeholder="请输入主题名称"
              maxlength="30"
              show-word-limit
              size="large"
              clearable
            />
          </el-form-item>
        </el-form>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <div class="btn cancel-btn" @click="closeTopicDialog">取 消</div>
          <div class="btn primary-btn" @click="saveTopic">保 存</div>
        </div>
      </template>
    </el-dialog>

    <!-- 新增题库弹窗 -->
    <el-dialog
      v-model="questionBankDialog.visible"
      :title="questionBankDialog.title"
      :width="questionBankDialog.width"
      :before-close="closeQuestionBankDialog"
      append-to-body
    >
      <div class="dialog-body">
        <el-form
          :model="questionBankForm"
          ref="questionBankFormRef"
          label-width="6rem"
        >
          <el-form-item
            label="题库名称"
            prop="name"
            :rules="[
              { required: true, message: '请输入题库名称', trigger: 'blur' },
            ]"
          >
            <el-input
              v-model="questionBankForm.name"
              placeholder="请输入主题名称"
              maxlength="30"
              show-word-limit
              size="large"
              clearable
            />
          </el-form-item>
          <el-form-item
            label="关联主题"
            prop="topic_ids"
            :rules="[
              { required: true, message: '请选择关联主题', trigger: 'blur' },
            ]"
          >
            <el-select
              v-model="questionBankForm.topic_ids"
              placeholder="请选择关联主题"
              multiple
              filterable
              size="large"
            >
              <el-option
                v-for="item in topicOptions"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              />
            </el-select>
          </el-form-item>
        </el-form>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <div class="btn cancel-btn" @click="closeQuestionBankDialog">
            取 消
          </div>
          <div class="btn primary-btn" @click="saveQuestionBank">保 存</div>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import draggable from "vuedraggable";
import { useUserStore } from "@/store/modules/user";

import { useRoute, useRouter } from "vue-router";

import {
  parseTime,
  resetReactiveObject,
  secondsToHoursAndMinutes,
  numberToChinese,
  numberToChinese2,
} from "@/utils";
import { checkUserPermission } from "@/utils/auth";
import {
  getTopics,
  addTopic,
  updateTopic,
  deleteTopic,
  getQuestionBanks,
  addQuestionBank,
  updateQuestionBank,
  deleteQuestionBank,
  getQuestionBankCourses,
} from "@/api/exam";
import { getCourses } from "@/api/course";
const { proxy } = getCurrentInstance() as any;

defineOptions({
  name: "QuestionBank",
  inheritAttrs: false,
});

const route = useRoute();
const router = useRouter();
const loading = ref<any>(false);
const total = ref<any>(0);

const queryParams = reactive<any>({
  search: "",
  pageNum: 1,
  pageSize: 20,
});

const activeTopic = ref<any>(0);
const sidebarTopics = ref<any>([
  {
    id: 0,
    name: "全部",
  },
]);
const topicId = ref<any>(null);

const tableData = ref<any>([]);
const rowId = ref<any>(null);

// 10-单选题，20-多选题，30-填空题，40-判断题
const questionTypeOptions = ref<any>([
  { label: "单选题", value: 10 },
  { label: "多选题", value: 20 },
  { label: "判断题", value: 30 },
  { label: "填空题", value: 40 },
]);
// 题目类型映射
const questionTypeMap = reactive<any>({
  10: "单选题",
  20: "多选题",
  30: "判断题",
  40: "填空题",
});

// 关联课程弹窗数据
const linkLoading = ref(false);
const linkCourseDialog = reactive<any>({
  visible: false,
  width: "50%",
  title: "关联课程",
});
const linkCourseForm = reactive<any>({
  currentQuestionBank: null as any,
  leftSearch: "",
  rightSearch: "",
  selectedUnlinkedIds: [] as number[], //  多选
  selectedLinkedIds: [] as number[], //   多选
});

// 课程数据
const unLinkedCourses = ref<any>([]);
const linkedCourses = ref<any>([]);
const allCourses = computed(() => {
  return [
    ...unLinkedCourses.value.map((item) => {
      item.linked = false;
      return item;
    }),
    ...linkedCourses.value.map((item) => {
      item.linked = true;
      return item;
    }),
  ];
});

// 过滤未关联课程
const filteredUnlinkedCourses = computed(() => {
  return allCourses.value
    .filter((course) => !course.linked)
    .filter((course) =>
      course.name
        .toLowerCase()
        .includes(linkCourseForm.leftSearch.toLowerCase())
    );
});

//  过滤已关联课程
const filteredLinkedCourses = computed(() => {
  return allCourses.value
    .filter((course) => course.linked)
    .filter((course) =>
      course.name
        .toLowerCase()
        .includes(linkCourseForm.rightSearch.toLowerCase())
    );
});

// 未关联课程全选状态
const isAllUnlinkedSelected = computed(() => {
  const filtered = filteredUnlinkedCourses.value;
  return (
    filtered.length > 0 &&
    filtered.every((course) =>
      linkCourseForm.selectedUnlinkedIds.includes(course.id)
    )
  );
});

// 未关联课程半选状态
const isUnlinkedIndeterminate = computed(() => {
  const filtered = filteredUnlinkedCourses.value;
  const selectedCount = filtered.filter((course) =>
    linkCourseForm.selectedUnlinkedIds.includes(course.id)
  ).length;
  return selectedCount > 0 && selectedCount < filtered.length;
});

// 已关联课程全选状态
const isAllLinkedSelected = computed(() => {
  const filtered = filteredLinkedCourses.value;
  return (
    filtered.length > 0 &&
    filtered.every((course) =>
      linkCourseForm.selectedLinkedIds.includes(course.id)
    )
  );
});

// 已关联课程半选状态
const isLinkedIndeterminate = computed(() => {
  const filtered = filteredLinkedCourses.value;
  const selectedCount = filtered.filter((course) =>
    linkCourseForm.selectedLinkedIds.includes(course.id)
  ).length;
  return selectedCount > 0 && selectedCount < filtered.length;
});

//主题相关
const topicDialog = reactive<any>({
  visible: false,
  title: "新增主题",
  width: "25%",
});
const topicForm = reactive<any>({
  name: "",
});
const topicFormRef = ref<any>();

// 题库相关
const questionBankDialog = reactive<any>({
  visible: false,
  title: "新增题库",
  width: "25%",
});
const questionBankFormRef = ref<any>(null);
const questionBankForm = reactive<any>({
  name: "",
  enabled: "",
  topic_ids: "",
  questions: [],
});
const questionData = reactive<any>({
  name: "", //题目标题
  image_urls: "", //图片的路径列表，如：["/image/platform/xxx.jpg", "/image/platform/xxx.jpg"]
  remark: "", //题目解析
  q_type: "",
  content: "", // 当题目类型为选择题（p_type=10和20）时，为json格式的选项内容，如：[{"A": "xxx"}, {"B": "xxx"}]
  answers: "", // 答案，有多个时用逗号隔开，如："A, B"
  seq: "",
});
const topicOptions = ref<any>([]);

onBeforeMount(() => {});

onMounted(() => {
  getData();
  // getCoursesData();
  getTopicsData();
});

function getQuestionTypeText(type: any) {
  return questionTypeMap[type] || " ";
}

function handleQuery() {
  queryParams.pageNum = 1;
  getData();
}

function handleCreate() {
  questionBankDialog.visible = true;
  // router.push({
  //   path: "question-bank-action",
  //   query: { type: "create" },
  // });
}

function getData() {
  loading.value = true;
  const params = {
    search: queryParams.search || undefined,
    page: queryParams.pageNum,
    per_page: queryParams.pageSize,
    topic_id: topicId.value === 0 ? undefined : topicId.value,
  };
  // setTimeout(() => {
  //   total.value = tableData.value.length;
  //   loading.value = false;
  // }, 500);
  getQuestionBanks(params).then((res: any) => {
    if (res.status === 200) {
      tableData.value = res.data.q_banks.map((item: any) => {
        item.created_at = parseTime(item.created_at, "{y}-{m}-{d} {h}:{i}:{s}");
        return item;
      });
      total.value = res.total;
      loading.value = false;
    }
  });
}
function onRowClick(type: string, row: any) {
  rowId.value = row.id;
  switch (type) {
    case "link":
      openLinkCourseDialog(row);
      break;
    case "status":
      const message = row.enabled === false ? "启用成功" : "停用成功";
      const text = row.enabled === true ? "停用" : "启用";
      ElMessageBox.confirm("此操作将" + text + "该题库，是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        updateQuestionBank(row.id, { enabled: !row.enabled }).then(() => {
          ElMessage.success(message);
          getData();
        });
      });
      break;

    case "detail":
      router.push({
        path: "question-bank-detail",
        query: { id: row.id, type: "detail" },
      });
      break;
    case "edit":
      router.push({
        path: "question-bank-action",
        query: { id: row.id, type: "edit" },
      });
      break;
    case "delete":
      handleDelete(row);
      break;
  }
}

function handleDelete(row: any) {
  ElMessageBox.confirm(`确定要删除题库"${row.name}"吗？`, "删除确认", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  }).then(() => {
    deleteQuestionBank(row.id).then((res: any) => {
      if (res.status === 200) {
        ElMessage.success("删除成功");
        getData();
      }
    });
  });
}

// 关联课程相关方法

//获取关联课程
async function getCoursesData() {
  try {
    linkCourseDialog.visible = true;
    linkLoading.value = true;
    const params = {
      page: 1,
      per_page: 9999,
      has_q_bank: 0,
    };
    await getQuestionBankCourses(params).then((res: any) => {
      unLinkedCourses.value = res.data.courses.map((item: any) => {
        item.name = item.title;
        return item;
      });
    });
    const params2 = {
      page: 1,
      per_page: 999,
      has_q_bank: 1,
      q_bank: rowId.value,
    };
    await getQuestionBankCourses(params2).then((res: any) => {
      linkedCourses.value = res.data.courses.map((item: any) => {
        item.name = item.title;
        return item;
      });
    });
  } catch (e) {
    console.log("e:", e);
  } finally {
    linkLoading.value = false;
  }
}
async function openLinkCourseDialog(row: any) {
  linkCourseForm.leftSearch = "";
  linkCourseForm.rightSearch = "";
  linkCourseForm.selectedUnlinkedIds = [];
  await getCoursesData();
}

// 处理checkbox变化事件
function handleCheckboxChange(course?: any, type?: any, checked?: any) {
  if (type === "unlinked") {
    if (checked) {
      // 添加
      if (!linkCourseForm.selectedUnlinkedIds.includes(course.id)) {
        linkCourseForm.selectedUnlinkedIds.push(course.id);
      }
    } else {
      //  移除
      const index = linkCourseForm.selectedUnlinkedIds.indexOf(course.id);
      if (index > -1) {
        linkCourseForm.selectedUnlinkedIds.splice(index, 1);
      }
    }
  } else {
    if (checked) {
      // 添加
      if (!linkCourseForm.selectedLinkedIds.includes(course.id)) {
        linkCourseForm.selectedLinkedIds.push(course.id);
      }
    } else {
      //  移除
      const index = linkCourseForm.selectedLinkedIds.indexOf(course.id);
      if (index > -1) {
        linkCourseForm.selectedLinkedIds.splice(index, 1);
      }
    }
  }
}

// 点击课程项切换选择状态
function toggleCourseSelection(course?: any, type?: any) {
  if (type === "unlinked") {
    const isSelected = linkCourseForm.selectedUnlinkedIds.includes(course.id);
    handleCheckboxChange(course, type, !isSelected);
  } else {
    const isSelected = linkCourseForm.selectedLinkedIds.includes(course.id);
    handleCheckboxChange(course, type, !isSelected);
  }
}

// 处理未关联课程全选
function handleSelectAllUnlinked(checked?: any) {
  if (checked) {
    const allUnlinkedIds = filteredUnlinkedCourses.value.map(
      (course) => course.id
    );
    linkCourseForm.selectedUnlinkedIds = [...allUnlinkedIds];
  } else {
    linkCourseForm.selectedUnlinkedIds = [];
  }
}

// 处理已关联课程全选
function handleSelectAllLinked(checked?: any) {
  if (checked) {
    const allLinkedIds = filteredLinkedCourses.value.map((course) => course.id);
    linkCourseForm.selectedLinkedIds = [...allLinkedIds];
  } else {
    linkCourseForm.selectedLinkedIds = [];
  }
}

function addCourseLink() {
  if (linkCourseForm.selectedUnlinkedIds.length > 0) {
    let successCount = 0;
    linkCourseForm.selectedUnlinkedIds.forEach((courseId) => {
      const course = allCourses.value.find((c) => c.id === courseId);
      if (course) {
        course.linked = true;

        successCount++;
      }
    });

    linkCourseForm.selectedUnlinkedIds = [];
  }
}

function removeCourseLink() {
  if (linkCourseForm.selectedLinkedIds.length > 0) {
    let successCount = 0;
    linkCourseForm.selectedLinkedIds.forEach((courseId) => {
      const course = allCourses.value.find((c) => c.id === courseId);
      if (course) {
        course.linked = false;

        successCount++;
      }
    });

    linkCourseForm.selectedLinkedIds = [];
  }
}

function closeLinkCourseDialog() {
  linkCourseDialog.visible = false;
  linkCourseForm.selectedUnlinkedIds = [];
  linkCourseForm.selectedLinkedIds = [];
  linkedCourses.value = [];
  unLinkedCourses.value = [];
}

function saveLinkCourse() {
  const data = {
    course_ids: allCourses.value
      .filter((item: any) => item.linked)
      .map((item: any) => item.id),
  };
  updateQuestionBank(rowId.value, data).then((res: any) => {
    if (res.status === 200) {
      ElMessage.success("关联成功");
      closeLinkCourseDialog();
      getData();
    }
  });
}

function endVideosMove() {
  //  批量更新排序
  const updatePromises = sidebarTopics.value
    .filter((item) => item.id !== 0) // 跳过“全部”或不需要排序的项
    .map((item, index) => updateTopic(item.id, { seq: index }));
  Promise.all(updatePromises)
    .then((r) => {
      console.log("r:", r);
    })
    .catch((e) => {
      console.log("e:", e);
    });
}

// 左侧主题操作
function getTopicsData() {
  const params = {
    per_page: 9999,
    page: 1,
  };
  getTopics(params).then((res: any) => {
    sidebarTopics.value = [
      { id: 0, name: "全部", seq: 0 },
      ...res.data.qb_topic,
    ];
    topicOptions.value = res.data.qb_topic;
  });
}

function handleTopicClick(type?: any, row?: any) {
  if (type === "active") {
    topicId.value = row?.id;
    activeTopic.value = row?.id;
    queryParams.topic_id = row.id;
    getData();
  }
  if (type === "create") {
    topicDialog.visible = true;
    topicForm.name = "";
    topicDialog.title = "新增主题";
  }
  if (type === "edit") {
    topicId.value = row.id;
    topicDialog.title = "编辑主题";
    topicDialog.visible = true;
    topicForm.name = row?.name || "";
    topicForm.id = row?.id || undefined;
  }
  if (type === "delete") {
    handleDeleteTopic(row);
  }
}
function closeTopicDialog() {
  topicDialog.visible = false;
  topicForm.name = "";
  topicForm.id = undefined;
  (topicFormRef.value as any).resetFields();
}
function saveTopic() {
  (topicFormRef.value as any).validate((valid: boolean) => {
    if (!valid) return;
    if (topicForm.id) {
      updateTopic(topicForm.id, { name: topicForm.name }).then((res: any) => {
        if (res.status === 200) {
          ElMessage.success("编辑成功");
          closeTopicDialog();
          getTopicsData();
        }
      });
    } else {
      addTopic({ name: topicForm.name }).then((res: any) => {
        if (res.status === 200) {
          ElMessage.success("新增成功");
          closeTopicDialog();
          getTopicsData();
        }
      });
    }
  });
}

function handleDeleteTopic(row?: any) {
  ElMessageBox.confirm(`确定要删除${row.name} 主题吗？`, "删除确认", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  }).then(() => {
    deleteTopic(topicId.value).then(() => {
      ElMessage.success("删除成功");
      handleTopicClick("active", { id: 0 });
      getTopicsData();
    });
  });
}

//新增题库
function closeQuestionBankDialog() {
  resetReactiveObject(questionBankForm);
  questionBankFormRef.value?.resetFields();
  questionBankFormRef.value?.clearValidate();
  questionBankDialog.visible = false;
}

function saveQuestionBank() {
  questionBankFormRef.value.validate((valid: boolean) => {
    if (!valid) return;
    const data: any = {
      name: questionBankForm.name,
      enabled: true,
      topic_ids: questionBankForm.topic_ids,
    };
    addQuestionBank(data).then((res: any) => {
      if (res.status === 200) {
        ElMessage.success("新增成功");
        router.push({
          path: "question-bank-action",
          query: { id: res.data.q_bank.id, type: "edit" },
        });
      }
    });
  });
}
</script>

<style scoped lang="scss">
.question-bank-container {
  position: relative;
  // background: #fff;
  // border-radius: 8px;
  // box-shadow: 0 3px 20px 1px rgb(109 125 126 / 7%);
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 95%;
  margin: 20px;

  .left-sidebar {
    position: relative;
    display: flex;
    flex-direction: column;
    align-items: center;
    width: 15%;
    height: 100%;
    padding: 0;
    background: #fff;
    border-radius: 12px;
    box-shadow: 0 3px 20px 1px rgb(109 125 126 / 7%);

    .sidebar-list {
      display: flex;
      flex-direction: column;
      align-items: center;
      width: 100%;

      .drag-container {
        display: flex;
        flex-direction: column;
        align-items: center;
        width: 100%;

        .green-item {
          display: flex;
          align-items: center;
          width: 100%;
          min-height: 60px;
          padding: 20px 0;
          font-size: 16px;
          font-weight: 500;
          color: #3b4664;
          cursor: pointer;
          border-bottom: 1px solid #c1c7d5;

          &:hover {
            // border: 1px solid #00918c !important;
            background: #f0f9ff;
          }

          .green-title {
            display: flex;
            align-items: center;
            justify-content: space-between;
            width: 100%;
            padding: 0 10px;

            .icon {
              display: flex;
              align-items: center;
              justify-content: center;
              margin-right: 10px;
              cursor: pointer;
            }

            .left {
              display: flex;
              align-items: center;
              justify-content: center;
              width: 80%;

              .text {
                width: 80%;
                // overflow: hidden;
                // text-overflow: ellipsis;
                // white-space: nowrap;
              }

              svg {
                cursor: move;
              }
            }

            .right {
              display: flex;
              align-items: center;
              width: 20%;

              span {
                display: inline-block;
              }
            }
          }
        }

        .green-item:first-child {
          height: 44px;
          min-height: 44px;
          padding: 10px 0;
          border-radius: 8px 8px 0 0;

          .green-title,
          .left,
          .text {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 100% !important;
          }

          .icon {
            margin: 0 !important;
          }
        }

        .green-item:last-child {
          // border-bottom: transparent;
        }

        .active-item {
          color: #fff !important;
          background: linear-gradient(180deg, #00918c 0%, #1c8d84 100%);

          &:hover {
            color: #3b4664 !important;
          }
        }
      }

      .sidebar-item {
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 10px 0;
        cursor: pointer;
      }
    }

    .sidebar-footer {
      display: flex;
      align-items: center;
      justify-content: space-around;
      margin-top: 15px;
      // position: relative;
    }
  }

  .right-content {
    width: 84%;
    height: 100%;
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 3px 20px 1px rgb(109 125 126 / 7%);
  }

  .pre-header {
    width: 100%;
    padding: 20px 20px 0;
  }

  .container-header {
    display: flex;
    justify-content: space-between;
    width: 100%;
    padding: 20px;

    .left {
      display: flex;
      align-items: center;
      justify-content: space-between;
      // width: 60%;
      .btn {
        width: 116px;
        height: 40px;
        margin-left: 20px;
        font-size: 17px;
      }
    }

    .right {
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .filter-row {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 260px;
      margin-left: 20px;

      &:nth-child(1) {
        width: 260px;
        margin-left: 0;
      }

      .btn {
        width: 116px;
        height: 40px;
        margin-left: 20px;
      }
    }
  }

  .content {
    width: 100%;
    height: calc(100% - 170px);
    padding: 10px 20px;

    .course-tags {
      display: flex;
      flex-wrap: wrap;
      gap: 5px;
    }
  }

  .footer {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-top: 15px;
  }
}

// 关联课程弹窗样式
.link-course-dialog {
  .link-course-content {
    width: 100%;

    .course-selection {
      display: flex;
      gap: 20px;
      height: 50vh;

      .course-list {
        display: flex;
        flex-direction: column;
        width: calc(50% - 40px);
        border: 1px solid #e4e7ed;
        border-radius: 12px 12px 0 0;

        .list-header {
          display: flex;
          align-items: center;
          justify-content: center;
          height: 68px;
          padding: 10px;
          font-weight: 500;
          color: white;
          text-align: center;
          border-radius: 12px 12px 0 0;

          .header-title {
            font-size: 18px;
            font-weight: 500;
          }
        }

        .unlinked {
          background: url("@/assets/exam/grey-header.png") no-repeat;
          background-size: 100% 100%;
        }

        .linked {
          background: url("@/assets/exam/green-header.png") no-repeat;
          background-size: 100% 100%;
        }

        .search-box {
          display: flex;
          align-items: center;
          padding: 10px;
          border-bottom: 1px solid #e4e7ed;

          .select-all-box {
            padding: 0 20px 0 10px;
          }
        }

        .course-items {
          flex: 1;
          width: 100%;
          padding: 10px;
          overflow-y: auto;

          .course-item {
            width: 100%;
            padding: 8px;
            cursor: pointer;

            .el-checkbox {
              max-width: 90%;
              line-height: 1.5;
              word-break: break-all;
              white-space: normal;
            }
          }
        }
      }

      .operation-buttons {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 80px;

        .btn-group {
          display: flex;
          flex-direction: column;
          gap: 10px;

          .right-btn {
            width: 40px;
            height: 40px;
            cursor: pointer;
            background: url("@/assets/images/move-right.png") no-repeat;
            background-size: 100% 100%;
          }

          .left-btn {
            width: 40px;
            height: 40px;
            cursor: pointer;
            background: url("@/assets/images/move-left.png") no-repeat;
            background-size: 100% 100%;
          }
        }
      }
    }

    .tips {
      padding: 10px;
      margin-top: 20px;
      background-color: #fff6f7;
      border: 1px solid #fbc4c4;
      border-radius: 4px;

      .tip-text {
        font-size: 12px;
        color: #e6a23c;
      }
    }
  }
}
</style>
