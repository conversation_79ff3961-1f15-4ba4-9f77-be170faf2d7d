<template>
  <div class="question-item">
    <!-- 题目标题 -->
    <div class="question-header">
      <div class="question-header-left">
        <div class="label">
          {{ questionTypeLabel }}
        </div>
        <div class="input">
          <el-input
            v-model="localData.title"
            placeholder="请输入题目 "
            type="textarea"
            maxlength="500"
            show-word-limit
            size="large"
            :rows="2"
          />
        </div>
      </div>
      <div class="question-actions">
        <div class="drag-sort">
          <svg-icon icon-class="drag3" />
        </div>
        <div class="delete-icon" @click="handleDelete">
          <svg-icon icon-class="delete" />
        </div>
      </div>
    </div>

    <!-- 题目图片上传 TODO: -->
    <div class="question-img-list">
      <!-- 上传按钮 -->
      <div
        class="img-upload"
        v-if="!localData.images || localData.images.length < 6"
      >
        <svg-icon icon-class="default-img" @click="triggerImageUpload" />
        <div class="img-tips">
          上传图片，题目图片上限 6 张，每张不可超过 2 Mb
        </div>
      </div>

      <div
        class="upload-list"
        v-if="localData.images && localData.images.length > 0"
      >
        <!-- 已上传的图片 -->
        <div
          class="img-item"
          v-for="(img, imgIndex) in localData.images"
          :key="imgIndex"
        >
          <!-- <img :src="img.url" :alt="`图片${imgIndex + 1}`" /> -->
          <el-image
            class="el-img"
            :src="img.url"
            :zoom-rate="1.1"
            :max-scale="7"
            :min-scale="0.2"
            :preview-src-list="[img.url]"
            :preview-teleported="true"
            :close-on-press-escape="true"
            show-progress
            fit="cover"
          />
          <div class="img-delete" @click="removeImage(imgIndex)">
            <svg-icon icon-class="close2" />
          </div>
        </div>
      </div>

      <!-- 不需要原生的上传交互，素材库取代了 -->
      <!-- 隐藏的文件上传输入框 -->
      <!-- <input
        ref="imageUploadRef"
        type="file"
        accept="image/*"
        multiple
        style="display: none"
        @change="handleImageUpload"
      /> -->
    </div>

    <!-- 题目编辑表单 -->
    <div class="question-form">
      <!-- 选项 -->
      <div class="question-options">
        <div class="label">
          {{
            localData.q_type == 30 || localData.q_type == 40 ? " 答案" : "选项"
          }}
          <i-ep-circle-plus
            @click="addOption"
            v-if="
              localData.options &&
              localData.options.length < 6 &&
              (localData.q_type == 10 || localData.q_type == 20)
            "
          />
          <i-ep-circle-plus
            @click="addAnswer"
            v-else-if="localData.q_type == 30"
          />
          <!-- 单选和多选才可以添加选项 ，判断题也可以有多个答案（提交的答案用"/"分割）-->
        </div>
        <div class="green-block">
          <!-- 单选 -->
          <template v-if="localData.q_type == 10">
            <div class="options-container">
              <div
                class="option-item"
                v-for="(option, optIndex) in localData.options"
                :key="optIndex"
              >
                <div class="option-desc">
                  <div
                    class="option-label custom-checkbox-circle checkbox-circle-green2"
                  >
                    <svg-icon
                      icon-class="default-img"
                      class="img-icon"
                      @click="uploadOptionImage(optIndex)"
                      v-if="!localData.options[optIndex].img"
                    />

                    <el-checkbox
                      :model-value="
                        localData.correctAnswer ===
                        String.fromCharCode(65 + optIndex)
                      "
                      @change="
                        (checked) => handleSingleChoice(checked, optIndex)
                      "
                    />
                    <span class="option-letter"
                      >{{ String.fromCharCode(65 + optIndex) }}.</span
                    >
                  </div>
                  <div class="input">
                    <el-input
                      v-model="option.text"
                      placeholder="请输入答案"
                      maxlength="100"
                      show-word-limit
                      size="large"
                      clearable
                    />
                  </div>
                  <div
                    class="delete-icon"
                    @click="removeOption(optIndex)"
                    v-if="localData.options.length > 2"
                  >
                    <svg-icon icon-class="delete" />
                  </div>
                </div>
                <div class="img-item" v-if="localData.options[optIndex].img">
                  <!-- <img
                    :src="localData.options[optIndex].img"
                    :alt="`图片${optIndex + 1}`"
                  /> -->
                  <el-image
                    class="el-img"
                    :src="localData.options[optIndex].img"
                    :zoom-rate="1.1"
                    :max-scale="7"
                    :min-scale="0.2"
                    :preview-src-list="[localData.options[optIndex].img]"
                    :preview-teleported="true"
                    :close-on-press-escape="true"
                    show-progress
                    fit="cover"
                  />
                  <div class="img-delete" @click="removeOptionImage(optIndex)">
                    <svg-icon icon-class="close2" />
                  </div>
                </div>
              </div>
            </div>
          </template>

          <!-- 多选 -->
          <template v-if="localData.q_type == 20">
            <div class="options-container">
              <div
                class="option-item"
                v-for="(option, optIndex) in localData.options"
                :key="optIndex"
              >
                <div class="option-desc">
                  <div
                    class="option-label custom-checkbox-circle checkbox-circle-green2"
                  >
                    <svg-icon
                      icon-class="default-img"
                      class="img-icon"
                      @click="uploadOptionImage(optIndex)"
                      v-if="!localData.options[optIndex].img"
                    />
                    <el-checkbox
                      :model-value="
                        localData.correctAnswers &&
                        localData.correctAnswers.includes(
                          String.fromCharCode(65 + optIndex)
                        )
                      "
                      @change="(checked?: any) => handleMultipleChoice(checked, optIndex)"
                    />

                    <span class="option-letter"
                      >{{ String.fromCharCode(65 + optIndex) }}.</span
                    >
                  </div>
                  <div class="input">
                    <el-input
                      v-model="option.text"
                      placeholder="请输入答案"
                      maxlength="100"
                      show-word-limit
                      size="large"
                      clearable
                    />
                  </div>
                  <div
                    class="delete-icon"
                    @click="removeOption(optIndex)"
                    v-if="localData.options.length > 2"
                  >
                    <svg-icon icon-class="delete" />
                  </div>
                </div>
                <div class="img-item" v-if="localData.options[optIndex].img">
                  <!-- <img
                    :src="localData.options[optIndex].img"
                    :alt="`图片${optIndex + 1}`"
                  /> -->
                  <el-image
                    class="el-img"
                    :src="localData.options[optIndex].img"
                    :zoom-rate="1.1"
                    :max-scale="7"
                    :min-scale="0.2"
                    :preview-src-list="[localData.options[optIndex].img]"
                    :preview-teleported="true"
                    :close-on-press-escape="true"
                    show-progress
                    fit="cover"
                  />
                  <div class="img-delete" @click="removeOptionImage(optIndex)">
                    <svg-icon icon-class="close2" />
                  </div>
                </div>
              </div>
            </div>
          </template>

          <!-- 填空 -->
          <template v-if="localData.q_type == 30">
            <div class="options-container">
              <div
                class="option-item"
                v-for="(answer, answerIndex) in localData.answers"
                :key="answerIndex"
              >
                <div class="option-desc">
                  <div class="option-label">答案{{ answerIndex + 1 }}:</div>
                  <div class="input">
                    <el-input
                      v-model="localData.answers[answerIndex]"
                      placeholder="请输入答案"
                      maxlength="100"
                      show-word-limit
                      size="large"
                      clearable
                    />
                  </div>
                  <div
                    class="delete-icon"
                    @click="removeAnswer(answerIndex)"
                    v-if="localData.answers.length > 1"
                  >
                    <svg-icon icon-class="delete" />
                  </div>
                </div>
              </div>
            </div>
          </template>

          <!-- 判断 -->
          <template v-if="localData.q_type == 40">
            <div class="options-container">
              <div class="option-item">
                <div class="option-desc">
                  <div
                    class="option-label custom-checkbox-circle checkbox-circle-green2"
                  >
                    <!-- <svg-icon icon-class="default-img" /> -->
                    <el-checkbox
                      :model-value="localData.correctAnswer === 'A'"
                      @change="(checked?: any) => handleJudgmentChoice(checked, 'A')"
                    />
                    <span class="option-letter">A. 正确</span>
                  </div>
                </div>
              </div>
              <div class="option-item">
                <div class="option-desc">
                  <div
                    class="option-label custom-checkbox-circle checkbox-circle-green2"
                  >
                    <!-- <svg-icon icon-class="default-img" /> -->
                    <el-checkbox
                      :model-value="localData.correctAnswer === 'B'"
                      @change="(checked?: any) => handleJudgmentChoice(checked, 'B')"
                    />
                    <span class="option-letter">B. 错误</span>
                  </div>
                </div>
              </div>
            </div>
          </template>
        </div>
      </div>

      <!-- 解析 -->
      <div class="question-analysis">
        <div class="label">解析</div>
        <div class="green-block">
          <div class="input">
            <el-input
              v-model="localData.remark"
              placeholder="请输入解析"
              type="textarea"
              maxlength="500"
              show-word-limit
              size="large"
              :rows="2"
            />
          </div>
        </div>
      </div>
    </div>

    <!-- 图片素材弹窗 -->
    <el-dialog
      class="imgDialog"
      v-model="imgDialog.visible"
      :title="imgDialog.title"
      :width="imgDialog.width"
      append-to-body
      @close="closeImgDialog"
    >
      <div class="dialog-body">
        <el-form>
          <el-form-item class="table-item">
            <div class="table-search">
              <div class="filter-row">
                <el-input
                  v-model="queryParams.search"
                  placeholder="请输入关键字"
                  clearable
                  size="large"
                />
              </div>
              <div class="btn primary-btn" @click="handleQuery">
                <i-ep-search /> 搜索
              </div>
            </div>
            <el-table
              v-loading="loading"
              element-loading-text="Loading"
              element-loading-background="#ffffffb4"
              :data="tableData"
              height="20rem"
              border
              fit
              highlight-current-row
              ref="tableRef"
              @select="selectItem"
            >
              <el-table-column
                type="selection"
                align="center"
                min-width="20"
                class="dialog-checkbox2"
              />

              <el-table-column
                label="名称"
                align="center"
                prop="name"
                min-width="80"
              >
                <template #default="scope">
                  <span>{{ scope.row.name }}</span>
                </template>
              </el-table-column>
              <el-table-column
                label="大小"
                align="center"
                prop="name"
                min-width="40"
              >
                <template #default="scope">
                  <span>{{ scope.row.size }}Mb</span>
                </template>
              </el-table-column>
            </el-table>
            <div class="table-footer">
              <pagination
                v-if="total > 0"
                v-model:total="total"
                v-model:page="queryParams.pageNum"
                v-model:limit="queryParams.pageSize"
                @pagination="handleQuery"
              />
            </div>
          </el-form-item>
        </el-form>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <div class="btn cancel-btn" @click="closeImgDialog">取 消</div>
          <div class="btn primary-btn" @click="handleSelectedSubmit">确 定</div>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
//素材列表接口
import { getResources } from "@/api/resource";
import { parseTime } from "@/utils";
import { ElMessage } from "element-plus";

defineOptions({
  name: "QuestionItem",
  inheritAttrs: false,
});

const props = defineProps({
  questionData: {
    type: Object,
    required: true,
  },
  questionIndex: {
    type: Boolean,
    required: true,
  },
  questionTypeMap: {
    type: Object,
    default: () => ({
      10: { label: "单选" },
      20: { label: "多选" },
      30: { label: "填空" },
      40: { label: "判断" },
    }),
  },
});

const emit = defineEmits(["delete", "update"]);

// 创建本地响应式数据副本，避免直接修改 props，避免循环
const localData: any = computed(() => props.questionData);

// 图片上传相关
const imageUploadRef = ref<HTMLInputElement>();
// 素材弹窗相关
const queryParams = reactive<any>({
  search: "",
  pageNum: 1,
  pageSize: 20,
  res_type: 20,
});
const total = ref(0); // 数据总数
const tableData = ref<any>([]);
const loading = ref(false);
const imgDialog = reactive<any>({
  visible: false,
  title: "试题图片素材",
  width: "50%",
});
const tableRef = ref();
const currentImg = reactive<any>({});

// 监听 props 变化，同步到本地数据
// watch(
//   () => props.questionData,
//   (newValue) => {
//     localData.value = { ...newValue };
//   },
//   { deep: true, immediate: true }
// );

// 监听本地数据变化，实时同步到父组件
watch(
  localData,
  (newValue) => {
    console.log("localData.value---newValue", newValue);
    emit("update", newValue);
  },
  { deep: true }
);

const questionTypeLabel = computed(() => {
  return props.questionTypeMap[localData.value.q_type]?.label + ".题目";
});

// 初始化数据
onMounted(() => {
  // 确保数据结构完整
  if (!localData.value.images) {
    localData.value.images = [];
  }
  if (!localData.value.correctAnswers && localData.value.q_type === 20) {
    localData.value.correctAnswers = [];
  }
});

// 图片上传相关
function triggerImageUpload() {
  // imageUploadRef.value?.click();
  getResourceData();
  imgDialog.visible = true;
  imgDialog.type = "title-img";
}
function getResourceData() {
  loading.value = true;
  const params = {
    search: queryParams.search || undefined,
    page: queryParams.pageNum,
    per_page: queryParams.pageSize,
    type: queryParams.res_type,
  };
  getResources(params).then((res: any) => {
    tableData.value = res.data.resources.map((item: any) => {
      item.created_at = parseTime(item.created_at, "{y}-{m}-{d} {h}:{i}:{s}");
      // item.size = (item.size / 1024 / 1024)?.toFixed(1);
      item.size = (item.size / 1024 / 1024)?.toFixed(2);
      if (item.res_type !== 10) {
        item.size = item.res_info.size
          ? (item.res_info.size / 1024 / 1024)?.toFixed(2)
          : (item.size / 1024 / 1024)?.toFixed(2);
      }
      return item;
    });
    total.value = res.total;
    loading.value = false;
  });
}
function handleQuery() {
  getResourceData();
}
function handleSelectedSubmit() {
  const res_info: any =
    typeof currentImg.res_info === "string"
      ? JSON.parse(currentImg.res_info)
      : currentImg.res_info;

  //分情况（题目的图片和选项的图片）
  if (imgDialog.type == "title-img") {
    localData.value.images.push({
      url: res_info?.url || null,
    });
  } else if (imgDialog.type == "answer-img") {
    localData.value.options[imgDialog.optIndex].img = res_info?.url || null;
  }

  closeImgDialog();
}
function selectItem(selection: any) {
  let selectRow: any = {};
  if (selection.length > 1) {
    let del_row = selection.shift(); // 删除选中的第一项
    tableRef.value.toggleRowSelection(del_row, false); //并改变table勾选状态
  }
  // 到这selection数据始终为1条
  if (selection.length) {
    selectRow = selection[0];
  }
  Object.assign(currentImg, {
    name: selectRow.name,
    res_info: selectRow.res_info,
    id: selectRow.id,
  });
}
function closeImgDialog() {
  tableRef.value.clearSelection();
  Object.assign(currentImg, {
    name: "",
    res_info: "",
    id: "",
  });
  Object.assign(queryParams, {
    search: "",
    pageNum: 1,
    pageSize: 20,
    res_type: 20,
  });

  imgDialog.visible = false;
}
function removeImage(index?: any) {
  if (localData.value.images) {
    localData.value.images.splice(index, 1);
  }
}

//题目中选项的图片上传相关--只有单选和多选需要,q_type==10,q_type==20
function uploadOptionImage(optIndex?: any) {
  // imageUploadRef.value?.click();
  getResourceData();
  imgDialog.visible = true;
  imgDialog.type = "answer-img";
  imgDialog.optIndex = optIndex;
}
function removeOptionImage(index?: any) {
  delete localData.value.options[index].img;
}

// function handleImageUpload(event: Event) {
//   const target = event.target as HTMLInputElement;
//   const files = target.files;
//   if (!files) return;

//   const remainingSlots = 6 - (localData.value.images?.length || 0);
//   const filesToProcess = Array.from(files).slice(0, remainingSlots);

//   filesToProcess.forEach((file) => {
//     // 检查文件大小 (2MB)
//     if (file.size > 2 * 1024 * 1024) {
//       ElMessage.warning(`图片 ${file.name} 超过 2MB，已跳过`);
//       return;
//     }

//     // 检查文件类型
//     if (!file.type.startsWith("image/")) {
//       ElMessage.warning(`文件 ${file.name} 不是图片格式，已跳过`);
//       return;
//     }

//     // 创建预览URL
//     const reader = new FileReader();
//     reader.onload = (e) => {
//       if (!localData.value.images) {
//         localData.value.images = [];
//       }
//       localData.value.images.push({
//         url: e.target?.result as string,
//         file: file,
//         name: file.name,
//       });
//     };
//     reader.readAsDataURL(file);
//   });

//   // 清空input值，允许重复选择同一文件
//   target.value = "";
//   console.log("localData.value.images", localData.value.images);
// }

// 选项操作函数
function handleSingleChoice(checked?: any, optIndex?: any) {
  if (checked) {
    localData.value.correctAnswer = String.fromCharCode(65 + optIndex);
  } else {
    localData.value.correctAnswer = "";
  }
}

function handleMultipleChoice(checked?: any, optIndex?: any) {
  const optionKey = String.fromCharCode(65 + optIndex);
  if (!localData.value.correctAnswers) {
    localData.value.correctAnswers = [];
  }

  if (checked) {
    if (!localData.value.correctAnswers.includes(optionKey)) {
      localData.value.correctAnswers.push(optionKey);
    }
  } else {
    const index = localData.value.correctAnswers.indexOf(optionKey);
    if (index > -1) {
      localData.value.correctAnswers.splice(index, 1);
    }
  }

  // 重新生成当前所有选项的key
  const validKeys = localData.value.options.map((item: any, idx: number) =>
    String.fromCharCode(65 + idx)
  );
  // 过滤掉不在选项中的答案
  const correctAnswers = localData.value.correctAnswers
    .filter((key: string) => validKeys.includes(key))
    .sort();
  Object.assign(localData.value, { correctAnswers });
  // console.log("localData.value.correctAnswers", localData.value);
}

function handleJudgmentChoice(checked?: any, option?: any) {
  if (checked) {
    localData.value.correctAnswer = option;
  } else {
    localData.value.correctAnswer = "";
  }
}

// 题目操作函数
function handleDelete() {
  emit("delete", props.questionIndex, localData.value);
}

// 添加选项
function addOption() {
  if (localData.value.options && localData.value.options.length < 6) {
    localData.value.options.push({ text: "" });
  }
}
//填空题添加答案
function addAnswer() {
  if (localData.value.answers && localData.value.answers.length < 6) {
    localData.value.answers.push("");
  }
}

// 删除选项
function removeOption(optIndex?: any) {
  if (localData.value.options && localData.value.options.length > 2) {
    localData.value.options.splice(optIndex, 1);
    const deletedOptionKey = String.fromCharCode(65 + optIndex);

    // 单选题：如果删除的是正确答案，清空
    if (
      localData.value.q_type == 10 &&
      localData.value.correctAnswer === deletedOptionKey
    ) {
      localData.value.correctAnswer = "";
    }

    // 多选题：如果删除的是正确答案之一，移除
    if (
      localData.value.q_type == 20 &&
      Array.isArray(localData.value.correctAnswers)
    ) {
      // 重新生成当前所有选项的key
      const validKeys = localData.value.options.map((item: any, idx: number) =>
        String.fromCharCode(65 + idx)
      );
      // 过滤掉不在选项中的答案
      const correctAnswers = localData.value.correctAnswers
        .filter((key: string) => validKeys.includes(key))
        .sort();
      Object.assign(localData.value, { correctAnswers });
    }
  }
}

//填空题删除答案
function removeAnswer(answerIndex: number) {
  if (localData.value.answers && localData.value.answers.length > 1) {
    localData.value.answers.splice(answerIndex, 1);
  }
}
</script>

<style scoped lang="scss">
.question-item {
  width: 100%;
  padding: 20px;
  background: #fff;
  border: 1px solid #edeff4;
  border-radius: 8px;
  box-shadow: inset 3px 3px 6px 1px rgb(188 198 214 / 25%);
  // margin-bottom: 20px;

  // &:last-child {
  //   margin-bottom: 0;
  // }

  .el-img {
    width: 172px;
    height: 96px;
    cursor: pointer;
    object-fit: cover;
    border-radius: 8px;
  }

  .label {
    display: flex;
    flex-direction: column;
    // justify-content: center;
    align-items: center;
    width: 80px;
    min-width: 80px;

    svg {
      margin-top: 10px;
      font-size: 28px;
      color: #00918c;
      cursor: pointer;
    }
  }

  .input {
    width: 100%;
  }

  .question-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    padding: 10px 0;
    margin-bottom: 15px;

    .question-header-left {
      display: flex;
      gap: 12px;
      width: 100%;
      font-size: 15px;
      font-weight: 500;
      color: #3b4664;
    }

    .question-actions {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: space-between;
      height: 70px;
      padding: 0 10px;

      .drag-sort {
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 5px;
        font-size: 24px;
        color: #666;
        cursor: move;
        border-radius: 4px;
        transition: all 0.2s ease;

        &:hover {
          color: #333;
          background-color: #f5f5f5;
        }

        svg {
          pointer-events: none;
        }
      }

      .delete-icon {
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 24px;
        cursor: pointer;
      }
    }
  }

  .question-img-list {
    display: flex;
    flex-flow: column wrap;
    gap: 10px;
    margin-bottom: 15px;
    margin-left: 100px;

    .img-upload {
      display: flex;
      align-items: center;
      // justify-content: center;
      font-size: 27px;

      svg {
        cursor: pointer;
      }

      .img-tips {
        margin-left: 10px;
        font-size: 14px;
        font-weight: 400;
        line-height: 1;
        color: #8d9295;
      }
    }

    .upload-list {
      display: flex;
      flex-wrap: wrap;
      gap: 20px;
    }
  }

  .img-item {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 172px;
    height: 96px;
    background: #61c7bc;
    border: 1px solid #dcdfe6;
    border-radius: 8px;

    img {
      width: 172px;
      height: 96px;
      // width: 162px;
      // height: 86px;
      object-fit: cover;
      border-radius: 8px;
    }

    .img-delete {
      position: absolute;
      top: -12px;
      right: -12px;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 19px;
      cursor: pointer;
      border-radius: 50%;

      &:hover {
        transform: scale(1.2);
      }
    }
  }

  .question-form {
    .green-block {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 100%;
      min-height: 116px;
      padding: 24px;
      background: linear-gradient(180deg, #eaf8e5 0%, #e2f8e2 100%);
      border: 1px solid #fff;
      border-radius: 13px;
      box-shadow: 0 0 6px 1px rgb(191 226 206 / 16%);

      :deep(.el-textarea__inner),
      :deep(.el-input__inner) {
        background: #fff !important;
      }
    }
  }

  .question-analysis {
    display: flex;
    gap: 12px;
    justify-content: space-between;
    width: 100%;
    padding: 10px 0;
    padding-right: 20px;
    margin-bottom: 15px;
  }

  .question-options {
    display: flex;
    gap: 12px;
    justify-content: space-between;
    width: 100%;
    padding: 10px 0;
    padding-right: 20px;
    margin-bottom: 15px;

    .options-container {
      display: flex;
      flex-wrap: wrap;
      gap: 10px;
      width: 100%;
      background: #fff;
      border: 1px solid #edeff4;
      border-radius: 8px;
      box-shadow: inset 3px 3px 6px 1px rgb(188 198 214 / 14%);

      :deep(.el-textarea__inner),
      :deep(.el-input__wrapper) {
        background-color: none !important;
        border: none !important;
        border-radius: none !important;
        box-shadow: none !important;
      }

      .option-item {
        position: relative;
        display: flex;
        // align-items: center;
        flex-direction: column;
        width: 100%;
        padding: 10px;
        border-bottom: 1px solid #c1c7d5;

        .option-desc {
          position: relative;
          display: flex;
          align-items: center;
          width: 100%;
        }

        .option-label {
          position: relative;
          display: flex;
          align-items: center;
          justify-content: center;
          min-width: 80px;
          padding: 0 10px;

          .img-icon {
            cursor: pointer;
          }

          svg {
            margin-right: 10px;
            font-size: 28px;
          }

          .option-letter {
            margin-left: 10px;
            font-weight: 500;
            color: #333;
          }
        }

        .img-item {
          margin-left: 20px;

          .img-delete {
            font-size: 19px !important;
          }
        }

        .delete-icon {
          margin-left: 10px;
          cursor: pointer;
        }

        &:last-child {
          border-bottom: none;
        }
      }
    }
  }
}
</style>
<style lang="scss">
.imgDialog {
  .table-item {
    width: 100%;

    .table-search {
      display: flex;
      width: 100%;
      margin-bottom: 20px;

      .filter-row {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 360px;
        margin-left: 20px;

        &:nth-child(1) {
          width: 260px;
          margin-left: 0;
        }
      }

      .primary-btn {
        width: 116px !important;
        margin-left: 20px !important;
      }
    }

    .el-table {
      .el-table__header-wrapper {
        .el-checkbox {
          display: none;
          visibility: hidden;
        }
      }
    }

    .table-footer {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 100%;
    }
  }
}
</style>
