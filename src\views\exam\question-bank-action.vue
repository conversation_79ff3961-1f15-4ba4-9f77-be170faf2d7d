<template>
  <div class="question-bank-action-container">
    <div class="container-header">
      <div class="left">
        <div class="btn primary-btn" @click="handleBack">返回</div>
        <span
          >题库--{{ type == "edit" ? questionBankForm?.name : "" }}
          {{ typeMap[type].label }}
        </span>
      </div>
      <div class="right">
        <div class="btn primary-btn" @click="handleSubmit">保存</div>
      </div>
    </div>
    <div class="content">
      <el-scrollbar
        style="width: 100%; height: 100%"
        warp-style="overflow-x: hidden;"
        class="exam-scrollbar"
      >
        <div class="base-info">
          <div class="title">基本信息</div>
          <el-row>
            <el-col :span="12" align="center">
              <div class="label"><span>*</span>题库名称</div>
              <div class="input">
                <el-input
                  v-model="questionBankForm.name"
                  placeholder="请输入题库名称"
                  clearable
                  size="large"
                />
              </div>
            </el-col>
            <el-col :span="12" align="center">
              <div class="label"><span>*</span>关联主题</div>
              <div class="input">
                <el-select
                  size="large"
                  v-model="questionBankForm.topic_ids"
                  placeholder="请选择关联主题"
                  filterable
                  clearable
                  :teleported="false"
                  :suffix-icon="`CaretBottom`"
                  multiple
                >
                  <el-option
                    v-for="item in topicOptions"
                    :key="item.value"
                    :label="item.name"
                    :value="item.id"
                  />
                </el-select>
              </div>
            </el-col>
          </el-row>
        </div>
        <div class="paper-info">
          <div class="title">题目</div>
          <div class="q-type-action">
            <div class="left">
              <div class="label">题目类型</div>
              <div
                class="btn q-type-btn"
                v-for="(item, index) in questionOptions"
                :key="index"
                @click="handleQuestionAdd(item.value)"
              >
                {{ item.label }}
              </div>
              <div class="q-type-tips">点击题型手动添加题目</div>
              <div
                class="q-num-tips"
                v-if="questionBankForm.questions?.length > 0"
              >
                已添加{{ questionBankForm.questions?.length }}道题目
              </div>
            </div>

            <div class="right" v-if="type == 'edit'">
              <div class="text-btn" @click="handleImport">
                <i-ep-circle-plus style="margin-right: 5px" />
                导入题目
              </div>
              <!-- <el-upload
                class="upload-file question-uploader"
                ref="importRef"
                multiple
                :on-exceed="handleExceed"
                :auto-upload="false"
                :before-upload="handleBeforeUpload"
                :on-change="handleFileChange"
                :on-remove="handleRemove"
              >
               
                <template #trigger>
                  <div class="text-btn">
                    <i-ep-circle-plus style="margin-right: 5px" />
                    导入题目
                  </div>
                </template>
              </el-upload> -->
            </div>
          </div>
          <div
            class="q-list"
            :class="{ dragging: isDragging }"
            v-if="questionBankForm.questions?.length > 0"
          >
            <draggable
              v-model="questionBankForm.questions"
              class="question-drag-container"
              handle=".drag-sort"
              :animation="200"
              item-key="id"
              ghost-class="ghost-item"
              chosen-class="chosen-item"
              drag-class="drag-item"
              @start="onDragStart"
              @end="onDragEnd"
            >
              <template #item="{ element, index }">
                <div class="question-item-wrapper">
                  <QuestionItem
                    :key="element.id || index"
                    :question-data="element"
                    :question-index="index"
                    :question-type-map="questionMap"
                    @delete="handleQuestionDelete"
                    @update="handleQuestionUpdate"
                  />
                </div>
              </template>
            </draggable>
          </div>
        </div>
      </el-scrollbar>
    </div>

    <el-dialog
      class="import-dialog"
      v-model="importDialog.visible"
      :title="importDialog.title"
      :width="importDialog.width"
      append-to-body
      @close="closeImportDialog"
    >
      <div class="dialog-body">
        <div class="import-file">
          <div class="el-upload-tip">
            <div class="import-text">
              支持上传文件类型：DOCX
              <div class="text-btn" @click="handleDownload">下载模板</div>
            </div>
          </div>
          <div class="el-upload-tip">
            <div class="import-text">
              单次限上传1000道题目，文件大小不可超过200MB
            </div>
          </div>

          <el-upload
            class="upload-file staff"
            ref="importRef"
            multiple
            :on-exceed="handleExceed"
            :auto-upload="false"
            :before-upload="handleBeforeUpload"
            :on-change="handleFileChange"
            :on-progress="handleFileProgress"
            :on-remove="handleRemove"
            :limit="1"
          >
            <!-- :limit="4" -->
            <template #trigger>
              <div class="btn primary-btn">上传文件</div>
            </template>
          </el-upload>
        </div>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <div class="btn cancel-btn" @click="closeImportDialog">
            <span>取 消</span>
          </div>
          <div class="btn primary-btn" @click="handleSubmitImport">
            <span>确 定</span>
          </div>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { useUserStore } from "@/store/modules/user";
import { useRoute, useRouter } from "vue-router";
import {
  parseTime,
  resetReactiveObject,
  secondsToHoursAndMinutes,
  numberToChinese,
  numberToChinese2,
} from "@/utils";
import { checkUserPermission } from "@/utils/auth";
import draggable from "vuedraggable";
import type { UploadProps, UploadRawFile } from "element-plus";
import { genFileId } from "element-plus";
import {
  getQuestionBankDetail,
  addQuestionBank,
  updateQuestionBank,
  getTopics,
  downloadTemplate,
  questionsImport,
} from "@/api/exam";

import QuestionItem from "./components/QuestionItem.vue";

const { proxy } = getCurrentInstance() as any;
// proxy 替代 this
defineOptions({
  name: "QuestionBankAction",
  inheritAttrs: false,
});
const userStore = useUserStore();
const route = useRoute();
const router = useRouter();
const questionMap = reactive<any>({
  10: { label: "单选" },
  20: { label: "多选" },
  30: { label: "填空" },
  40: { label: "判断" },
});
const questionOptions = ref<any>([
  { label: "单选题", value: 10 },
  { label: "多选题", value: 20 },
  { label: "填空题", value: 30 },
  { label: "判断题", value: 40 },
]);

const type: any = route.query.type;
const questionBankId: any = route.query.id;
const typeMap = reactive<any>({
  create: { value: "create", label: "新增" },
  edit: { value: "edit", label: "修改" },
  detail: { value: "detail", label: "详情" },
});

const topicOptions = ref<any>([]);

const questionBankForm = reactive<any>({
  name: "",
  enabled: "",
  topic_ids: "",
  questions: [],
});
const questionData = reactive<any>({
  name: "", //题目标题
  image_urls: "", //图片的路径列表，如：["/image/platform/xxx.jpg", "/image/platform/xxx.jpg"]
  remark: "", //题目解析
  q_type: "",
  content: "", // 当题目类型为选择题（q_type=10和20）时，为json格式的选项内容，如：[{"A": "xxx"}, {"B": "xxx"}]
  answers: "", // 答案，有多个时用逗号隔开，如："A, B"
  seq: "",
});

//导入题目
const importRef = ref<any>(null);
const importFiles = ref<any>([]);
const importDialog = reactive<any>({
  visible: false,
  type: "import",
  width: "25%",
  title: "导入题目",
});

onBeforeMount(() => {});
onMounted(() => {
  const route = useRoute();
  getTopicsData();
  if (route.query.id || route.params.id) {
    getQBDetail(route.query.id || route.params.id);
  }
});

function getTopicsData() {
  const params = {
    per_page: 9999,
    page: 1,
  };
  getTopics(params).then((res: any) => {
    topicOptions.value = [...res.data.qb_topic];
  });
}

async function getQBDetail(id: any) {
  try {
    const response = await getQuestionBankDetail(id);
    const data = response.data.q_bank;
    Object.assign(questionBankForm, data);
    questionBankForm.topic_ids = data.gk_topics;
    // .toString();
    questionBankForm.questions = data.questions.map((item: any, index: any) => {
      let question: any = {
        id: item.id,
        // name: item.name,
        title: item.name,
        // images: item.images?.map((item: any) => {
        //   return { url: item };
        // }),
        remark: item.remark,
        q_type: item.q_type || item.category,
        content: item.content,
        answers: item.answers,
        correctAnswer: item.answers,
        seq: item.seq || index,
      };
      const images =
        typeof item.images == "string" ? JSON.parse(item.images) : item.images;
      if (images && images.length) {
        question.images = images.map((item: any) => {
          return { url: item };
        });
      }
      if (question.q_type === 40) {
        question.correctAnswer = formatAnswer(question.answers);
        question.answers = formatAnswer(question.answers);
      }
      if (question.q_type === 20) {
        question.correctAnswers = question.answers.split(",");
      }
      if (question.q_type === 30) {
        // 填空题：将答案字符串转换为数组
        question.answers = question.answers
          ? question.answers.split("/")
          : [""];
      }
      if (question.q_type === 10 || question.q_type === 20) {
        // 单选题或多选题
        const content =
          typeof question.content == "string"
            ? JSON.parse(question.content)
            : question.content;
        question.options = content.map((option: any) => {
          return {
            text: Object.values(option)[0],
            img: option.img || option.thumb,
          };
        });
      }
      return question;
    });
  } catch (error) {
    console.error("err:", error);
  }
}
// 注：判断题答案返回和提交是1/0，显示的答案要转换格式--A-1，B-0
function formatAnswer(value?: any) {
  if (!value) return "";
  return value == "1" ? "A" : "B";
}
function formatAnswer2(value?: any) {
  if (!value) return "";
  return value == "A" ? "1" : "0";
}

function handleQuestionAdd(type: any, index?: any) {
  const newQuestion: any = {
    // id: Date.now() + Math.random(), // 添加唯一ID
    name: "",
    // image_urls: "",
    images: [],
    remark: "",
    q_type: type,
    content: "",
    answers: "",
    seq: questionBankForm.questions.length + 1,
    title: "",
  };

  // 根据题型初始化不同的数据结构
  if (type === 10) {
    // 单选题
    newQuestion.options = [
      { text: "" },
      { text: "" },
      { text: "" },
      { text: "" },
    ];
    newQuestion.correctAnswer = "";
  } else if (type === 20) {
    // 多选题
    newQuestion.options = [
      { text: "" },
      { text: "" },
      { text: "" },
      { text: "" },
    ];
    newQuestion.correctAnswers = [];
  } else if (type === 30) {
    // 填空题
    newQuestion.answers = [""];
  } else if (type === 40) {
    // 判断题
    newQuestion.correctAnswer = "";
  }
  questionBankForm.questions.unshift(newQuestion);
}

// QuestionItem 组件事件处理

// 实时更新
function handleQuestionUpdate(updatedData: any) {
  const index = questionBankForm.questions.findIndex(
    (q: any) => q.q_type === updatedData.q_type && q.seq === updatedData.seq
  );
  if (index !== -1) {
    Object.assign(questionBankForm.questions[index], updatedData);
  }
}

// 拖拽相关事件处理
const isDragging = ref(false);

function onDragStart(evt: any) {
  isDragging.value = true;
}

function onDragEnd(evt: any) {
  isDragging.value = false;

  questionBankForm.questions.forEach((question: any, index: number) => {
    question.seq = index + 1;
  });
}

function handleQuestionDelete(index: any, item: any) {
  questionBankForm.questions.splice(index, 1);
}

function handleBack() {
  router.go(-1);
}

function handleSubmit() {
  if (!questionBankForm.name) {
    ElMessage.warning("请输入题库名称");
    return;
  }
  if (!questionBankForm.topic_ids || questionBankForm.topic_ids.length == 0) {
    ElMessage.warning("请选择主题");
    return;
  }
  if (!questionBankForm.questions.length) {
    ElMessage.warning("请添加题目");
    return;
  }
  const data: any = {
    name: questionBankForm.name,
    enabled: true,
    topic_ids: questionBankForm.topic_ids,
    // .toString().split(","),
  };
  data.questions = questionBankForm.questions.map(
    (question: any, index: any) => {
      let content = "";
      if (question.q_type === 10 || question.q_type === 20) {
        // 单选题或多选题
        if (question.options && Array.isArray(question.options)) {
          // JSON.stringify()
          content = question.options.map((option: any, index: number) => {
            const key = String.fromCharCode(65 + index); // A, B, C, D...
            return {
              [key]: option?.text || "",
              img: option?.img || undefined,
            };
          });
        }
      }
      const newQuestion = {
        id: type === "create" ? undefined : question?.id,
        name: question.name || question.title,
        image_urls: question.images.length
          ? question.images.map((item: any) => item.url)
          : undefined,
        remark: question.remark,
        q_type: question.q_type,
        content: content || undefined,
        answers:
          question.q_type == 20
            ? question.correctAnswers.join(",")
            : question.q_type == 30
            ? question.answers.join("/")
            : question.correctAnswer,
        seq: index,
      };
      if (newQuestion.q_type == 40) {
        newQuestion.answers = formatAnswer2(newQuestion.answers);
      }
      console.log("newQuestion", newQuestion);
      return newQuestion;
    }
  );

  ElMessageBox.confirm("是否确认操作?", "提示", {
    confirmButtonText: "是",
    cancelButtonText: "否",
    type: "warning",
  }).then(() => {
    if (type === "create") {
      addQuestionBank(data).then((res: any) => {
        if (res.status == 200) {
          ElMessage.success({
            message: "新增成功!",
          });
          router.go(-1);
        }
      });
    } else if (type === "edit") {
      updateQuestionBank(questionBankId, data).then((res: any) => {
        if (res.status == 200) {
          ElMessage.success({
            message: "修改成功!",
          });
          router.go(-1);
        }
      });
    }
  });
}

//导入题目

function handleDownload() {
  try {
    downloadTemplate("试题模板").then((response: any) => {
      // 检查响应头是否存在
      const disposition = response.headers?.["content-disposition"];
      let filename = "试题模板.docx";

      // 从 Content-Disposition 中提取文件名
      if (disposition && disposition.includes("filename=")) {
        filename = decodeURIComponent(
          disposition.split("filename=")[1].replace(/['"]/g, "")
        );
      }
      // 检查响应数据是否存在
      if (response) {
        const blob = new Blob([response], {
          type: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
        });

        // 创建下载链接
        const link = document.createElement("a");
        link.href = URL.createObjectURL(blob);
        link.download = filename;

        // 触发下载
        document.body.appendChild(link);
        link.click();

        // 清理 DOM
        document.body.removeChild(link);
        URL.revokeObjectURL(link.href);
      } else {
        throw new Error("响应数据为空");
      }
    });
  } catch (error) {
    ElMessage.error("下载模板失败");
    console.error("下载模板失败:", error);
  }
}
function handleImport() {
  importDialog.visible = true;
}
function handleFileChange(file: any, res: any) {
  console.log("file", file);
  if (!file.name.includes(".docx")) {
    ElMessage.warning("只能上传DOCX文件");
    importRef.value?.clearFiles();
    return;
  }
  const l = importFiles.value.length;
  importFiles.value.push(file);
}

function handleFileProgress(file: any, res: any) {}
function handleBeforeUpload(file: any) {}
function handleRemove(file: any, fileList: any) {
  importFiles.value = fileList;
  console.log("remove", importFiles.value, file, fileList);
}
const handleExceed: UploadProps["onExceed"] = (files) => {
  importRef.value!.clearFiles();
  const file = files[0] as UploadRawFile;
  file.uid = genFileId();
  importRef.value!.handleStart(file);
};
function closeImportDialog() {
  importDialog.visible = false;
  importRef.value?.clearFiles();
  importFiles.value = [];
}
function handleSubmitImport() {
  console.log("importFiles.value", importFiles.value);
  if (!importFiles.value || importFiles.value.length == 0) {
    ElMessage.warning("请先上传文件");
    return;
  }

  // 收集所有导入请求
  const importPromises = importFiles.value.map((item: any) => {
    console.log("item", item);
    const data: any = {
      file: item.raw,
      q_bank_id: questionBankId,
    };
    return questionsImport(data);
  });

  Promise.all(importPromises)
    .then((res: any) => {
      console.log("导入结果", res);
      const hasError = res.some((item: any) => item.status == 400);
      if (hasError) {
        res.forEach((item: any) => {
          if (item.status == 400 && item.msg) {
            item.msg.forEach((msg: any) => {
              ElMessage.error(msg);
            });
          }
        });
      } else {
        ElMessage.success({
          message: "导入成功，题库已保存!",
        });
        closeImportDialog();
        getQBDetail(route.query.id || route.params.id);
      }
    })
    .catch((error) => {
      ElMessage.error("文件导入失败，请重试！");
    });
}
</script>

<style scoped lang="scss">
.question-bank-action-container {
  height: 95%;
  margin: 20px;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 3px 20px 1px rgb(109 125 126 / 7%);

  .container-header {
    display: flex;
    justify-content: space-between;
    padding: 20px;

    .left {
      display: flex;
      align-items: center;
      justify-content: space-between;
      font-size: 20px;
      font-weight: 500;
      color: #3b4664;

      .tips {
        margin-left: 30px;
        font-size: 14px;
        font-weight: 400;
        color: #f23c33;
      }

      .btn {
        width: 52px;
        height: 28px;
        margin-right: 10px;
        font-size: 13px;
        font-weight: 400;
        border-radius: 13px;
      }
    }

    .right {
      .btn {
        width: 96px;
        height: 42px;
        font-size: 18px;
        font-weight: 400;
        border-radius: 10px;
      }
    }
  }

  .content {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: calc(100% - 70px);
    padding: 10px 0;

    .exam-scrollbar,
    .el-scrollbar__wrap {
      width: 100%;
    }

    :deep(.el-scrollbar__view) {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      width: 100%;
    }

    .title {
      margin-left: 15px;
      font-size: 18px;
      font-weight: 500;
      color: #3b4664;
    }

    .el-row {
      margin: 20px 0;
    }

    .el-col {
      display: flex;
      align-items: center;
      padding: 0 20px;
    }

    .label {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 100px;
      font-size: 15px;
      font-weight: 400;
      color: #3b4664;

      span {
        color: red;
      }
    }

    .input {
      display: flex;
      align-items: center;
      justify-content: flex-start;
      width: 100%;

      img {
        width: 50%;
        height: 160px;
      }

      span {
        margin-top: 10px;
        font-size: 13px;
        font-weight: 400;
        color: #8d9295;
        text-align: left;
      }
    }
  }

  .base-info {
    width: 100%;
    padding: 0 20px 10px;
    border-bottom: 6px solid #edeeee;
  }

  .paper-info {
    width: 100%;
    padding: 20px 20px 10px;

    .q-type-action {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 20px;

      .q-type-tips {
        margin-left: 20px;
        font-size: 14px;
        font-weight: 400;
        color: #52b0a9;
      }

      .q-num-tips {
        margin-left: 20px;
        font-size: 20px;
        font-weight: 500;
        color: #00918c;
      }

      .q-type-btn {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 90px;
        height: 38px;
        margin-left: 10px;
        font-size: 15px;
        font-weight: 400;
        color: #6aaeae;
        cursor: pointer;
        background: #fff;
        border: 1px solid #6aaeae;
        border-radius: 19px;
        box-shadow: 0 3px 6px 1px rgb(107 174 175 / 25%);
      }

      .left {
        display: flex;
        align-items: center;
        justify-content: flex-start;
      }

      .right {
        display: flex;
        align-items: center;
        justify-content: flex-end;

        .text-btn {
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: 16px;
          font-weight: 400;
          cursor: pointer;

          svg {
            font-size: 28px;
          }
        }
      }
    }
  }

  .q-list {
    width: 100%;
    padding: 0 20px;

    .question-drag-container {
      width: 100%;
    }

    .question-item-wrapper {
      width: 100%;
      margin-bottom: 20px;
      background: #fff;

      &:last-child {
        margin-bottom: 0;
      }
    }
    // 拖拽相关样式
    // .ghost-item {
    // }

    // .chosen-item {
    //   opacity: 0.8;
    //   transform: scale(1.02);
    // }

    // .drag-item {
    //   opacity: 0.9;
    //   transform: rotate(1deg);
    //   box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
    // }

    // 拖拽过程中的动画效果
    :deep(.question-item) {
      transition: all 0.3s ease;

      &:hover {
        box-shadow: 0 6px 20px rgb(0 0 0 / 10%);
        transform: translateY(-2px);
      }
    }

    // 拖拽时禁用指针事件
    &.dragging {
      :deep(.question-item) {
        pointer-events: none;
      }
    }
  }
}
</style>
<style lang="scss">
.import-dialog {
  .import-file {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;

    .el-upload-tip {
      font-size: 12px;
      font-weight: 400;
      color: #8f9bb2;
    }

    .import-text {
      display: flex;
      align-items: center;
      margin-bottom: 10px;
    }

    .text-btn {
      margin-left: 20px;
    }
  }

  .upload-file {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    width: 100%;

    .btn {
      width: 374px !important;
      height: 50px !important;
      font-size: 15px;
      font-weight: 400;
      background: linear-gradient(360deg, #00918c 0%, #1c8d84 100%);
      border-radius: 6px;
      box-shadow: 0 3px 10px 1px rgb(111 165 159 / 49%),
        inset 0 3px 6px 1px rgb(229 253 251 / 36%);
    }
  }

  .el-upload-list {
    width: 374px !important;

    .el-upload-list__item {
      display: flex;
      align-items: center;
      height: 48px !important;
      font-size: 15px !important;
      font-weight: 400 !important;
      color: #3b4664 !important;
      background: linear-gradient(180deg, #eaf8e5 0%, #e2f8e2 100%);
      border: 1px solid #fff;
      border-radius: 8px;
      box-shadow: 0 0 6px 1px rgb(191 226 206 / 16%);
    }

    // .el-upload-list__item .el-icon--close {
    //   position: relative;
    //   z-index: 1;
    // }
    .el-upload-list__item .el-icon--close svg {
      display: none !important; // 隐藏原有svg
    }

    .el-upload-list__item .el-icon--close {
      display: inline-block;
      width: 21px;
      height: 20px;
      background: url("@/assets/images/delete.png") no-repeat;
      background-size: 100% 100%;
    }
  }

  .import-type {
    display: flex;
    align-items: center;
    padding: 0 30px;
    margin-top: 20px;
    font-size: 13px;
    font-weight: 400;
    color: #8d9295;

    span {
      margin-left: 10px;
    }
  }
}
</style>
