<template>
  <div class="exam-paper-container">
    <div class="questions">
      <component
        :is="questionMap[item.q_type].component"
        v-for="(item, index) in filteredQuestions"
        :key="index"
        :isOperable="isOperable"
        :showAllAnswer="showAllAnswer"
        :showSelectedAnswer="showSelectedAnswer"
        :showGetScore="showGetScore"
        :showQuestionScore="showQuestionScore"
        :showAnswerTime="showAnswerTime"
        :showAnalysis="showAnalysis"
        :showAnswerType="showAnswerType"
        :showCheckBox="showCheckBox"
        :showDrag="showDrag"
        :showDelete="showDelete"
        :itemStyle="itemStyle"
        :questionData="item"
        :questionIndex="index"
        :selectedQuestionIds="selectedQuestionIds"
        @update-answer="updateAnswer"
        @handle-action="handleAction"
      />
      <!-- @update-score="updateScore"
        @submit="submit" -->
    </div>
  </div>
</template>

<script setup lang="ts">
import { useAppStore } from "@/store/modules/app";
import { useRoute, useRouter } from "vue-router";

import FillBlank from "./FillBlank.vue";
import Judgement from "./Judgement.vue";
import MultipleChoice from "./MultipleChoice.vue";
import SingleChoice from "./SingleChoice.vue";
import { markRaw } from "vue";

//const { proxy } = getCurrentInstance();
// proxy 替代 this
defineOptions({
  name: "ExamPaper",
  inheritAttrs: false,
});

const props = defineProps({
  isOperable: {
    type: Boolean,
    default: false,
  }, //是否只读或答题
  showQuestionScore: {
    type: Boolean,
    default: false,
  }, //是否显示题目分数
  showAllAnswer: {
    type: Boolean,
    default: false,
  }, //是否显示答案
  showSelectedAnswer: {
    type: Boolean,
    default: false,
  }, //是否显示选中答案
  showGetScore: {
    type: Boolean,
    default: false,
  }, //是否显示得分
  showAnswerTime: {
    type: Boolean,
    default: false,
  }, //是否显示答题时间
  showCheckBox: {
    type: Boolean,
    default: false,
  }, //是否显示题目选中的选择框
  showAnalysis: {
    type: Boolean,
    default: false,
  }, //是否显示解析
  showAnswerType: {
    type: String,
    default: "all", // all-全部, correct-正确, incorrect-错误
  }, //筛选题目答完后答题类型
  itemStyle: {
    type: String,
    default: "bottomBorder", // shadowBorder-阴影边框, bottomBorder-底部边框，其它...
  }, //试题样式
  showDrag: {
    type: Boolean,
    default: false,
  }, //是否显示拖拽
  showDelete: {
    type: Boolean,
    default: false,
  }, //是否显示删除
  paperData: {
    type: Array,
    default: () => [],
  }, //试卷数据
  questionData: {
    type: Object,
    default: () => {
      return {};
    },
  }, //试题数据

  selectedQuestionIds: {
    type: Array,
    default: () => [],
  }, //外部传入的选中题目ID列表
  selectAllTrigger: {
    type: Boolean,
    default: false,
  }, //全选触发器
  selectNoneTrigger: {
    type: Boolean,
    default: false,
  }, //全不选触发器
});
const emit = defineEmits(["updateAnswer", "handleAction"]);
// "updateScore",
// "submit",
const store = useAppStore();
const route = useRoute();
const router = useRouter();

const isOperable = computed(() => props.isOperable);
const showAllAnswer = computed(() => props.showAllAnswer);
const showSelectedAnswer = computed(() => props.showSelectedAnswer);
const showGetScore = computed(() => props.showGetScore);
const showQuestionScore = computed(() => props.showQuestionScore);
const showAnswerTime = computed(() => props.showAnswerTime);
const itemStyle = computed(() => props.itemStyle);
const showCheckBox = computed(() => props.showCheckBox);
const showDrag = computed(() => props.showDrag);
const showDelete = computed(() => props.showDelete);

const showAnalysis = computed(() => props.showAnalysis);
const showAnswerType = computed(() => props.showAnswerType);

const paperDataMock = ref<any>([]);
const paperData = computed(() => props.paperData);
const questionData = computed(() => props.questionData);
// const paperData = reactive<any>({
//   // id	:'',//Integer	记录id
//   name: "", //String	试卷名称
//   remark: "", //String	试卷说明
//   time_limit: "", //	Integer	试卷限时，单位：分钟
//   scores: "", //	Integer	总分
//   questions: [], //Array	试题列表
// });
// const questionData = reactive<any>({
//   id: "", //	Integer	试题id
//   name: "", //	String	试题题目
//   scores: "", //	Integer	得分
//   q_type: "", //Integer	试题类型，10-单选，20-多选，30-填空，40-判断
//   content: "", //	String	题目内容，当题目类型为选择题（p_type=10和20）时，为json格式的选项内容，如：[{"A": "xxx"}, {"B": "xxx"}]
// user_answers:'',
//   answers: "", //String	题目的答案，有多个时用逗号隔开，如："A, B"
//   remark: "", //String	提示解析
// });
const questionMap = reactive<any>({
  10: { component: markRaw(SingleChoice), label: "单选题" },
  20: { component: markRaw(MultipleChoice), label: "多选题" },
  30: { component: markRaw(FillBlank), label: "填空题" },
  40: { component: markRaw(Judgement), label: "判断题" },
});

//数据处理和过滤
const filteredQuestions = computed(() => {
  const dataSource =
    paperData.value.length && Array.isArray(paperData.value)
      ? paperData.value
      : paperDataMock.value;

  const normalizedData = dataSource.map((item: any) => {
    if (item.q_type == 40 || item.category == 40) {
      console.log("添加判断题默认选项");
      item.content = JSON.stringify([{ A: "正确" }, { B: "错误" }]);
    }
    return normalizeQuestionData(item);
  });

  if (showAnswerType.value === "all") {
    return normalizedData;
  }

  // 根据答题类型过滤题目
  return normalizedData.filter((item: any) => {
    const userAnswer = item.userAnswer || "";
    const correctAnswer = item.answers || "";

    if (showAnswerType.value === "correct") {
      // return userAnswer === correctAnswer || item.is_correct;
      return item.is_correct;
    }
    if (showAnswerType.value === "wrong") {
      // return (
      //   (userAnswer !== correctAnswer && userAnswer !== "") || !item.is_correct
      // );
      return !item.is_correct;
    }
    return true;
  });
});

onBeforeMount(() => {});
onMounted(() => {});

//题目组件内的数据格式
function normalizeQuestionData(questionData: any) {
  let normalized: any = {
    id: questionData.id,
    q_type: questionData.q_type || questionData.category,
    title:
      questionData.name ||
      questionData.title ||
      getQuestionTypeLabel(questionData.q_type),
    images: questionData.image_urls || questionData.images,
    score:
      questionData.scores || questionData.score || questionData.q_scores || "",
    answers: questionData.answers || "",
    userAnswer: questionData.userAnswer || questionData.user_answers || "",
    // options: parseQuestionOptions(questionData),
    remark: questionData.remark || "",
    content: questionData.content || "",

    //用户答题后的详情
    is_correct: questionData.is_correct || false,
    user_scores: questionData.user_scores || questionData.scores || 0,
  };
  normalized.options = parseQuestionOptions(normalized);

  if (typeof normalized.images == "string") {
    normalized.images = JSON.parse(normalized.images);
  }
  return normalized;
}

// 解析题目选项
function parseQuestionOptions(questionData: any) {
  if (!questionData.content) return [];
  try {
    let content: any = [];
    // 如果content是字符串，尝试解析JSON
    if (typeof questionData.content === "string") {
      content = JSON.parse(questionData.content) || [];
    }
    // 如果content已经是数组，直接返回
    if (Array.isArray(questionData.content)) {
      content = questionData.content;
    }

    // let imgs: any = null;
    // if (typeof questionData.images == "string") {
    //   imgs = JSON.parse(questionData.images);
    // } else {
    //   imgs = questionData.images;
    // }
    // if (imgs && imgs.length) {
    //   content.forEach((item: any, index: any) => {
    //     item.img = imgs[index];
    //   });
    // }//不需要这个，content图片本身保存在选项中
    return content;
  } catch (error) {
    console.warn("Failed to parse question options:", error);
    return [];
  }
}

//  题目类型标签
function getQuestionTypeLabel(qType: number) {
  const typeMap: any = {
    10: "单选题",
    20: "多选题",
    30: "填空题",
    40: "判断题",
  };
  return typeMap[qType] || "";
}
const answersData = ref<any>([]);
const chosenQuestions = ref<any>([]);

// 监听全选触发器
watch(
  () => props.selectAllTrigger,
  (newVal) => {
    if (newVal) {
      selectAllQuestions();
    }
  }
);

// 监听全不选触发器
watch(
  () => props.selectNoneTrigger,
  (newVal) => {
    if (newVal) {
      selectNoneQuestions();
    }
  }
);

// 监听外部选中的题目ID列表
watch(
  () => props.selectedQuestionIds,
  (newIds) => {
    syncSelectedQuestions(newIds);
  },
  { deep: true }
);

// 答题数据
const userAnswers = ref<any>({});

// 更新答案
function updateAnswer(payload: any) {
  const { questionId, answer, questionType } = payload;

  //答案格式
  const normalizedAnswer = normalizeAnswer(answer, questionType);

  // 更新用户答案
  userAnswers.value[questionId] = {
    questionId,
    answer: normalizedAnswer,
    questionType,
    // timestamp: Date.now(),
  };

  //  答案更新
  emit("updateAnswer", {
    questionId,
    answer: normalizedAnswer,
    questionType,
    allAnswers: userAnswers.value,
  });
}

//答案格式
function normalizeAnswer(answer: any, questionType: number) {
  switch (questionType) {
    case 10: // 单选题
    case 40: // 判断题
      return typeof answer === "string" ? answer : String(answer);
    case 20: // 多选题
      if (Array.isArray(answer)) {
        return answer.join(",");
      }
      return typeof answer === "string" ? answer : String(answer);
    case 30: // 填空题
      if (typeof answer === "string") {
        // 如果用户输入包含'/'，保持原样；否则直接返回
        return answer.trim();
      }
      return String(answer).trim();
    default:
      return String(answer);
  }
}

// 更新分数？
// function updateScore(payload: any) {
//   const { questionId, score, maxScore } = payload;

//   emit("updateScore", {
//     questionId,
//     score,
//     maxScore,
//     timestamp: Date.now(),
//   });
// }

// 提交？
// function submit() {
//   const submissionData = {
//     answers: userAnswers.value,
//     submissionTime: Date.now(),
//     totalQuestions: filteredQuestions.value.length,
//     answeredQuestions: Object.keys(userAnswers.value).length,
//   };

//   emit("submit", submissionData);
// }
function selectAllQuestions() {
  // 全选所有题目
  chosenQuestions.value = [...filteredQuestions.value];
  const params = {
    type: "select-all",
    chosenQuestions: chosenQuestions.value,
  };
  emit("handleAction", params);
}

function selectNoneQuestions() {
  // 全不选
  chosenQuestions.value = [];
  const params = {
    type: "select-none",
    chosenQuestions: chosenQuestions.value,
  };
  emit("handleAction", params);
}

function syncSelectedQuestions(selectedIds: any[]) {
  // 根据外部传入的ID列表同步选中状态
  chosenQuestions.value = filteredQuestions.value.filter((question: any) =>
    selectedIds.includes(question.id)
  );
}

function handleAction(payload: any) {
  // 根据payload判断各种emit操作
  if (payload.type === "select-question") {
    // 检查题目是否已经在选中列表中
    const existingIndex = chosenQuestions.value.findIndex(
      (q: any) => q.id === payload.question.id
    );
    if (existingIndex === -1) {
      chosenQuestions.value.push(payload.question);
    }
    const params = {
      type: "select-question",
      chosenQuestions: chosenQuestions.value,
    };
    emit("handleAction", params);
  } else if (payload.type === "remove-question") {
    // 从选中列表中移除题目
    const index = chosenQuestions.value.findIndex(
      (q: any) => q.id === payload.question.id
    );
    if (index > -1) {
      chosenQuestions.value.splice(index, 1);
    }
    const params = {
      type: "remove-question",
      chosenQuestion: payload.question,
    };
    emit("handleAction", params);
  }

  if (payload.type === "delete-question") {
    const params = {
      type: "delete-question",
      question: payload.question,
    };
    emit("handleAction", params);
  }
}
</script>

<style scoped lang="scss">
.exam-paper-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100%;

  .questions {
    width: 100%;
  }
}
</style>
