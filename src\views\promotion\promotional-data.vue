<template>
  <div class="promotional-data-container">
    <div class="content">
      <div class="header">
        <!-- <div class="left-tag">
          <div class="top-text">
            <div class="left">专属推广码</div>
            <div class="right" @click="handleQrCode">
              查看二维码
              <svg-icon icon-class="arrow-right" />
            </div>
          </div>
          <div class="bottom-text">被推荐好友将获取专属优惠</div>
        </div> -->
        <div class="right-tag">
          <div class="item">
            <div class="num" style="color: #3b4664">
              {{ promotionData.scan_number || 0 }}
            </div>
            <div class="text">点击次数</div>
          </div>
          <div class="item">
            <div class="num" style="color: #00918c">
              {{ promotionData.total_orders }}
            </div>
            <div class="text">订单数量</div>
          </div>
          <div class="item">
            <div class="num" style="color: #f06e15">
              {{ promotionData.total_amount / 100 }}
            </div>
            <div class="text">收益（元）</div>
          </div>
        </div>
      </div>
      <div class="chart-content">
        <div class="chart-item">
          <div class="chart-pie">
            <div class="text">订单比例</div>
            <div
              class="order-ratio"
              :id="'order-ratio'"
              :style="{ height: '90%', width: '95%' }"
            ></div>
          </div>
          <div class="chart-bar">
            <div class="text">订单趋势</div>
            <div
              class="order-trends"
              :id="'order-trends'"
              :style="{ height: '95%', width: '98%' }"
            ></div>
          </div>
        </div>
        <div class="chart-item">
          <div class="chart-pie">
            <div class="text">收益比例</div>
            <div
              class="revenue-ratio"
              :id="'revenue-ratio'"
              :style="{ height: '90%', width: '95%' }"
            ></div>
          </div>
          <div class="chart-bar">
            <div class="text">收益趋势</div>
            <div
              class="revenue-trends"
              :id="'revenue-trends'"
              :style="{ height: '95%', width: '98%' }"
            ></div>
          </div>
        </div>
      </div>
    </div>

    <el-dialog
      class="qrDialog"
      v-model="qrDialog.visible"
      append-to-body
      :width="qrDialog.width"
      :title="qrDialog.title"
      @close="closeDialog"
    >
      <div class="dialog-body">
        <div class="qr-img">
          <VueQrcode
            :value="qrDialog.code_url"
            :size="70"
            :type="'image/png'"
            :color="{ dark: '#000', light: '#fff' }"
            ref="qrCodeCanvas"
          />
        </div>
      </div>
      <template #footer>
        <div class="dialog-footer footer1">
          <div class="btn primary-btn" @click="handleDownload">下载二维码</div>
          <div class="btn primary-btn" @click="closeDialog">确 定</div>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import VueQrcode from "vue-qrcode";
import { useAppStore } from "@/store/modules/app";
import { useRoute, useRouter } from "vue-router";
import * as echarts from "echarts";
import {
  parseTime,
  secondsToHoursAndMinutes,
  resetReactiveObject,
} from "@/utils";
import { getPromotionalData } from "@/api/promotion";
//const { proxy } = getCurrentInstance();
// proxy 替代 this
defineOptions({
  name: "PromotionalData",
  inheritAttrs: false,
});
const store = useAppStore();
const route = useRoute();
const router = useRouter();

const promotionData = reactive<any>({});
const qrDialog = reactive<any>({
  visible: false,
  width: "25%",
  title: "专属推广码",
  // code_url: "https://www.baidu.com",
});
const env_url: any = import.meta.env.VITE_APP_API_URL;
const pieColors = ref<any>([
  new echarts.graphic.LinearGradient(0, 0, 0, 1, [
    {
      offset: 0,
      color: "#F06E15",
    },
    {
      offset: 1,
      color: "#F79857",
    },
  ]),
  new echarts.graphic.LinearGradient(0, 0, 0, 1, [
    {
      offset: 0,
      color: "#33CEC9",
    },
    {
      offset: 1,
      color: "#1C8D84",
    },
  ]),
]);
const pieColors2 = ref<any>([
  new echarts.graphic.LinearGradient(0, 0, 0, 1, [
    {
      offset: 0,
      color: "#F06E15",
    },
    {
      offset: 1,
      color: "#F79857",
    },
  ]),
  new echarts.graphic.LinearGradient(0, 0, 0, 1, [
    {
      offset: 0,
      color: "#3CA575",
    },
    {
      offset: 1,
      color: "#5CCB8B",
    },
  ]),
]);
const textColors = ref<any>(["#F06E15", "#00918C"]);
const textColors2 = ref<any>(["#F06E15", "#0DB582"]);
const orderRatio = ref<any>([
  { value: 0, name: "会员订单" },
  { value: 0, name: "课程订单" },
]);
const orderRatioChart = ref<any>("");
const orderTrends = ref<any>([]);
const orderTrendsChart = ref<any>("");
const revenueRatio = ref<any>([
  { value: 0, name: "会员订单" },
  { value: 0, name: "课程订单" },
]);
const revenueRatioChart = ref<any>("");
const revenueTrends = ref<any>([]);
const revenueTrendsChart = ref<any>("");
onBeforeMount(() => {});
onMounted(async () => {
  await getData();
  nextTick(() => {
    chartInit();
  });
  // window.addEventListener("resize", () => {
  //   chartInit();
  // });
});
onBeforeUnmount(() => {
  // 组件卸载之前调用
  orderRatioChart.value.dispose();
  orderTrendsChart.value.dispose();
  revenueRatioChart.value.dispose();
  revenueTrendsChart.value.dispose();
});
async function getData() {
  const params = {};
  await getPromotionalData().then((res: any) => {
    Object.assign(promotionData, res.data);
    promotionData.total_orders =
      promotionData.course.total_orders + promotionData.vip.total_orders;
    promotionData.total_amount =
      promotionData.course.total_amount + promotionData.vip.total_amount;
    orderRatio.value[0].value = promotionData.vip.total_orders;
    orderRatio.value[1].value = promotionData.course.total_orders;
    revenueRatio.value[0].value = promotionData.vip.total_amount / 100;
    revenueRatio.value[1].value = promotionData.course.total_amount / 100;
    orderTrends.value = promotionData.gk_channel_code_stat.map((item) => {
      return {
        amount: item.total_orders,
        date: parseTime(item.stat_date, "{y}-{m}-{d}"),
      };
    });
    revenueTrends.value = promotionData.gk_channel_code_stat.map((item) => {
      return {
        amount: item.total_amount,
        date: parseTime(item.stat_date, "{y}-{m}-{d}"),
      };
    });
    qrDialog.code_url = `https://gknowledge.cn/promotion_code?channel_code_id=${res.data.channel_code}`;
    // qrDialog.code_url =
    //   env_url +
    //   `/gkapi/v1/promotion_code?channel_code_id= ${res.data.channel_code}`;
  });
}
function handleQrCode() {
  qrDialog.visible = true;
}
function handleDownload() {
  const img: any = document.querySelector(".qr-img img");
  const url = img.src;
  const a = document.createElement("a");
  a.href = url;
  a.download = "推广二维码.png";
  a.click();
}
function closeDialog() {
  qrDialog.visible = false;
}
function chartInit() {
  initOrderRatioData();
  initOrderTrendsData();
  initRevenueRatioData();
  initRevenueTrendsData();
}

function initOrderRatioData() {
  orderRatioChart.value = markRaw(
    echarts.init(document.getElementById("order-ratio") as HTMLDivElement)
  );
  const colorData = orderRatio.value.map((item, idx) => {
    item.label = {
      color: textColors.value[idx],
    };
    return item;
  });
  const options = {
    grid: {
      left: "2%",
      right: "2%",
      bottom: "10%",
      containLabel: true,
    },
    tooltip: {
      trigger: "item",
      formatter: "{b}: {c} ",
      border: "none",
    },

    series: [
      {
        name: " ",
        type: "pie",
        silent: true,
        selectedMode: "single",
        avoidLabelOverlap: true, // //是否启用防止标签重叠
        radius: [0, "28%"],
        center: ["50%", "50%"],
        itemStyle: {
          normal: {
            color: "transparent",
          },
        },
        label: {
          normal: {
            show: false,
          },
        },
        data: [{ value: 100 }],
      },
      {
        name: "mask",
        type: "pie",
        silent: true,
        radius: ["28%", "40%"],
        center: ["50%", "50%"],
        itemStyle: {
          normal: {
            opacity: 0.5,
          },
        },
        label: {
          normal: {
            show: false,
          },
        },
        data: colorData,
      },
      {
        name: " ",
        type: "pie",
        radius: ["40%", "55%"],
        center: ["50%", "50%"],
        label: {
          show: true,
          textBorderColor: "inherit",
          formatter: " {name|{b}}\n {prec| {d}%}", // 带当前图例名 + 百分比
          rich: {
            name: {
              fontSize: 12,
              fontWeight: 800,
            },
            prec: {
              lineHeight: 25,
              fontSize: 14,
              fontWeight: 400,
            },
          },
          fontWeight: "normal",
        },
        data: colorData,
      },
    ],
    color: pieColors.value,
  };
  orderRatioChart.value.setOption(options);
  // 大小自适应
  window.addEventListener("resize", () => {
    orderRatioChart.value.resize();
  });
}
function initOrderTrendsData() {
  orderTrendsChart.value = markRaw(
    echarts.init(document.getElementById("order-trends") as HTMLDivElement)
  );
  const hasData = orderTrends.value.map((item: any) => item.amount)[0];
  const options = {
    grid: {
      left: "2%",
      right: "2%",
      top: "24%",
      bottom: "10%",
      containLabel: true,
    },
    tooltip: {
      trigger: "axis",
      axisPointer: {
        type: "cross",
        crossStyle: {
          color: "#999",
        },
      },
    },

    xAxis: [
      {
        type: "category",
        data: orderTrends.value.map((item: any) => item.date),
        axisPointer: {
          type: "shadow",
        },
      },
    ],
    yAxis: [
      {
        type: "value",
        name: "单位：笔",
        scale: hasData ? true : false,
        min: 0,
        max: !hasData ? 15 : null,
        interval: !hasData ? 5 : null,
        axisLabel: {
          formatter: "{value} ",
        },
      },
    ],
    series: [
      {
        name: "数量",
        type: "bar",
        data: orderTrends.value.map((item: any) => item.amount),
        barWidth: "50%",
        itemStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            { offset: 0, color: "#00918C" },
            { offset: 1, color: "#1C8D84" },
          ]),
        },
      },
    ],
  };
  orderTrendsChart.value.setOption(options);
  // 大小自适应
  window.addEventListener("resize", () => {
    orderTrendsChart.value.resize();
  });
}
function initRevenueRatioData() {
  revenueRatioChart.value = markRaw(
    echarts.init(document.getElementById("revenue-ratio") as HTMLDivElement)
  );
  const colorData = revenueRatio.value.map((item, idx) => {
    item.label = {
      color: textColors2.value[idx],
    };
    return item;
  });
  const options = {
    grid: {
      left: "2%",
      right: "2%",
      bottom: "10%",
      containLabel: true,
    },
    tooltip: {
      trigger: "item",
      formatter: "{b}: {c} ",
      border: "none",
    },

    series: [
      {
        name: " ",
        type: "pie",
        silent: true,
        selectedMode: "single",
        avoidLabelOverlap: true, // //是否启用防止标签重叠
        radius: [0, "28%"],
        center: ["50%", "50%"],
        itemStyle: {
          normal: {
            color: "transparent",
          },
        },
        label: {
          normal: {
            show: false,
          },
        },
        data: [{ value: 100 }],
      },
      {
        name: "mask",
        type: "pie",
        silent: true,
        radius: ["28%", "40%"],
        center: ["50%", "50%"],
        itemStyle: {
          normal: {
            opacity: 0.5,
          },
        },
        label: {
          normal: {
            show: false,
          },
        },
        data: colorData,
      },
      {
        name: " ",
        type: "pie",
        radius: ["40%", "55%"],
        center: ["50%", "50%"],
        label: {
          show: true,
          textBorderColor: "inherit",
          formatter: " {name|{b}}\n {prec| {d}%}", // 带当前图例名 + 百分比
          rich: {
            name: {
              fontSize: 12,
              fontWeight: 800,
            },
            prec: {
              lineHeight: 25,
              fontSize: 14,
              fontWeight: 400,
            },
          },

          fontWeight: "normal",
        },
        data: colorData,
      },
    ],
    color: pieColors2.value,
  };
  revenueRatioChart.value.setOption(options);
  // 大小自适应
  window.addEventListener("resize", () => {
    revenueRatioChart.value.resize();
  });
}
function initRevenueTrendsData() {
  revenueTrendsChart.value = markRaw(
    echarts.init(document.getElementById("revenue-trends") as HTMLDivElement)
  );
  const hasData = revenueTrends.value.map((item: any) => item.amount / 100)[0];
  const options = {
    grid: {
      left: "2%",
      right: "2%",
      top: "24%",
      bottom: "10%",
      containLabel: true,
    },
    tooltip: {
      trigger: "axis",
      axisPointer: {
        type: "cross",
        crossStyle: {
          color: "#999",
        },
      },
    },

    xAxis: [
      {
        type: "category",
        data: revenueTrends.value.map((item: any) => item.date),
        axisPointer: {
          type: "shadow",
        },
      },
    ],
    yAxis: [
      {
        type: "value",
        name: "单位：元",
        scale: hasData ? true : false,
        min: 0,
        max: !hasData ? 300 : null,
        interval: !hasData ? 100 : null,
        axisLabel: {
          formatter: "{value} ",
        },
      },
    ],
    series: [
      {
        name: "金额",
        type: "bar",
        data: revenueTrends.value.map((item: any) => item.amount / 100),
        barWidth: "50%",
        itemStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            { offset: 0, color: "#0EB53B" },
            { offset: 1, color: "#0AC75B" },
          ]),
        },
      },
    ],
  };
  revenueTrendsChart.value.setOption(options);
  // 大小自适应
  window.addEventListener("resize", () => {
    revenueTrendsChart.value.resize();
  });
}
</script>

<style scoped lang="scss">
.promotional-data-container {
  height: 95%;
  margin: 20px;

  .content {
    width: 100%;
    height: 100%;
  }

  .header {
    display: flex;
    justify-content: space-between;
    width: 100%;
    height: 164px;
    border-radius: 0 8px 8px 0;
    box-shadow: 0 3px 20px 1px rgb(109 125 126 / 7%);

    .left-tag {
      display: flex;
      flex-direction: column;
      justify-content: center;
      width: 25%;
      height: 100%;
      padding: 0 30px;
      background: url("@/assets/promotion/header-tag1.png") no-repeat;
      background-size: 100% 100%;

      .top-text {
        display: flex;
        align-items: center;
        justify-content: space-between;

        .left {
          font-size: 30px;
          font-weight: 500;
          color: #3b4664;
        }

        .right {
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: 20px;
          font-weight: 400;
          color: #0d8883;
          cursor: pointer;

          svg {
            margin-left: 5px;
            font-size: 16px;
          }
        }
      }

      .bottom-text {
        margin-top: 10px;
        font-size: 18px;
        font-weight: 400;
        color: #3b4664;
      }
    }

    .right-tag {
      display: flex;
      align-items: center;
      justify-content: space-between;
      // width: 75%;
      width: 100%;
      background: #fff;
      box-shadow: 0 3px 20px 1px rgb(109 125 126 / 7%);

      .item {
        display: flex;
        flex: 1;
        flex-direction: column;
        align-items: center;
        justify-content: center;

        .num {
          font-size: 50px;
          font-weight: normal;
        }

        .text {
          font-size: 24px;
          font-weight: 400;
          color: #3b4664;
        }
      }
    }
  }

  .chart-content {
    display: flex;
    flex-flow: column wrap;
    align-items: center;
    justify-content: space-around;
    width: 100%;
    height: calc(100% - 164px);
    margin-top: 10px;

    .chart-item {
      position: relative;
      display: flex;
      align-items: center;
      justify-content: space-between;
      width: 100%;
      height: 48%;

      .text {
        position: absolute;
        top: 20px;
        left: 20px;
        font-size: 18px;
        font-weight: 500;
        color: #3b4664;
      }
    }

    .chart-pie {
      position: relative;
      display: flex;
      align-items: center;
      justify-content: center;
      width: 24.5%;
      height: 100%;
      background: #fff;
      border-radius: 8px;
      box-shadow: 0 3px 20px 1px rgb(109 125 126 / 7%);
    }

    .chart-bar {
      position: relative;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: flex-end;
      width: 74.5%;
      height: 100%;
      background: #fff;
      border-radius: 8px;
      box-shadow: 0 3px 20px 1px rgb(109 125 126 / 7%);
    }
  }
}
</style>
<style lang="scss">
.qrDialog {
  .qr-img {
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .footer1 {
    display: flex;
    align-items: center !important;
    justify-content: center !important;
  }
}
</style>
