<template>
  <div class="exam-index-container">
    <div class="container-header">
      <div class="left">
        <div class="filter-row">
          <el-input
            v-model="queryParams.search"
            placeholder="请输入关键字"
            clearable
            size="large"
          />
        </div>
        <div class="btn primary-btn" @click="handleQuery">
          <i-ep-search /> 搜索
        </div>
      </div>
      <div class="right">
        <div class="text-btn" @click="handleCreate">
          <i-ep-circle-plus style="margin-right: 5px" />
          新增考试
        </div>
      </div>
    </div>

    <div class="content">
      <el-table
        v-loading="loading"
        element-loading-text="Loading"
        element-loading-background="#ffffffb4"
        :data="tableData"
        height="100%"
        border
        fit
        highlight-current-row
      >
        <el-table-column label="标题" align="center" min-width="100">
          <template #default="scope">
            {{ scope.row.name }}
          </template>
        </el-table-column>
        <el-table-column label="关联课程" align="center" min-width="120">
          <template #default="scope">
            {{ scope.row.content?.name || "--" }}
          </template>
        </el-table-column>

        <!-- <el-table-column label="试卷发放规则" align="center" min-width="100">
          <template #default="scope">
            {{ scope.row?.paper_rule || "--" }}
          </template>
        </el-table-column> -->

        <el-table-column label="试卷数量" align="center" min-width="100">
          <template #default="scope">
            {{ scope.row.paper_qty }}
          </template>
        </el-table-column>
        <el-table-column label="创建人" align="center" min-width="60">
          <template #default="scope">
            {{ scope.row?.creator || "--" }}
          </template>
        </el-table-column>
        <el-table-column label="创建时间" align="center" min-width="120">
          <template #default="scope">
            {{ scope.row.created_at }}
          </template>
        </el-table-column>

        <el-table-column label="状态" align="center" min-width="80">
          <template #default="scope">
            <el-tag :type="scope.row.enabled === true ? 'success' : 'danger'">
              {{ scope.row.enabled === true ? "启用" : "停用" }}
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column label="操作" align="center" min-width="220">
          <template #default="scope">
            <div class="option-btn">
              <div
                class="btn light-blue-btn"
                @click="onRowClick('detail', scope.row)"
              >
                详情
              </div>
              <div
                class="btn primary-btn"
                @click="onRowClick('edit', scope.row)"
              >
                编辑
              </div>
              <div
                class="btn"
                :class="[
                  scope.row.enabled === false ? 'light-green-btn' : 'info-btn',
                ]"
                @click="onRowClick('status', scope.row)"
              >
                {{ scope.row.enabled === false ? "启用" : "停用" }}
              </div>
              <div
                class="btn delete-btn"
                @click="onRowClick('delete', scope.row)"
              >
                删除
              </div>
            </div>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <div class="footer">
      <pagination
        v-if="total > 0"
        v-model:total="total"
        v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize"
        @pagination="getData"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { useUserStore } from "@/store/modules/user";
import { status } from "nprogress";
import { useRoute, useRouter } from "vue-router";

import {
  parseTime,
  resetReactiveObject,
  secondsToHoursAndMinutes,
  numberToChinese,
  numberToChinese2,
} from "@/utils";
import { checkUserPermission } from "@/utils/auth";
import { getExams, getExamDetail, updateExam, deleteExam } from "@/api/exam";
const { proxy } = getCurrentInstance() as any;

defineOptions({
  name: "ExamIndex",
  inheritAttrs: false,
});

const route = useRoute();
const router = useRouter();
const loading = ref(false);
const total = ref(0);

const queryParams = reactive({
  search: "",
  pageNum: 1,
  pageSize: 20,
});

const tableData = ref<any>([]);

const rowId = ref<any>(0);
// 10-单选题，20-多选题，30-填空题，40-判断题
const questionTypeOptions = ref<any>([
  { label: "单选题", value: 10 },
  { label: "多选题", value: 20 },
  { label: "判断题", value: 30 },
  { label: "填空题", value: 40 },
]);
// 题目类型映射
const questionTypeMap = reactive<any>({
  10: "单选题",
  20: "多选题",
  30: "判断题",
  40: "填空题",
});

onBeforeMount(() => {});

onMounted(() => {
  getData();
});

function getData() {
  loading.value = true;

  const params = {
    search: queryParams.search || undefined,
    page: queryParams.pageNum,
    per_page: queryParams.pageSize,
  };

  getExams(params).then((res: any) => {
    if (res.status === 200) {
      tableData.value = res.data.exams.map((item: any) => {
        item.created_at = parseTime(item.created_at, "{y}-{m}-{d} {h}:{i}:{s}");
        return item;
      });
      total.value = res.total;
      loading.value = false;
    }
  });
}
function getLinkContent(content: any) {
  return "";
}

function handleQuery() {
  queryParams.pageNum = 1;
  getData();
}

function handleCreate() {
  console.log("新增");
  router.push({ path: "exam-action", query: { type: "create" } });
}

function onRowClick(type: string, row: any) {
  rowId.value = row.id;
  switch (type) {
    case "status":
      const newStatus = row.enabled === false ? "启用成功" : "停用成功";
      const statusText = row.enabled === true ? "停用" : "启用";
      ElMessageBox.confirm(
        "此操作将" + statusText + "该考试，是否继续?",
        "提示",
        {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        }
      ).then(() => {
        updateExam(row.id, { enabled: !row.enabled }).then(() => {
          ElMessage.success(newStatus);
          getData();
        });
      });
      break;
    case "detail":
      router.push({
        path: "exam-detail",
        query: { id: row.id, type: "detail" },
      });
      break;
    case "edit":
      router.push({ path: "exam-action", query: { id: row.id, type: "edit" } });
      break;
    case "delete":
      handleDelete(row);
      break;
  }
}

function handleDelete(row: any) {
  ElMessageBox.confirm(`确定要删除考试"${row.name}"吗？`, "删除确认", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  }).then(() => {
    deleteExam(row.id).then((res: any) => {
      if (res.status == 200) {
        ElMessage.success("删除成功");
        getData();
      }
    });
  });
}
</script>

<style scoped lang="scss">
.exam-index-container {
  height: 95%;
  margin: 20px;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 3px 20px 1px rgb(109 125 126 / 7%);

  .pre-header {
    width: 100%;
    padding: 20px 20px 0;
  }

  .container-header {
    display: flex;
    justify-content: space-between;
    width: 100%;
    padding: 20px;

    .left {
      display: flex;
      align-items: center;
      justify-content: space-between;
      // width: 60%;
      .btn {
        width: 116px;
        height: 40px;
        margin-left: 20px;
      }
    }

    .right {
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .filter-row {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 260px;
      margin-left: 20px;

      &:nth-child(1) {
        width: 260px;
        margin-left: 0;
      }

      .btn {
        width: 116px;
        height: 40px;
        margin-left: 20px;
      }
    }
  }

  .content {
    width: 100%;
    height: calc(100% - 170px);
    padding: 10px 20px;
  }

  .footer {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-top: 15px;
  }
}
</style>
