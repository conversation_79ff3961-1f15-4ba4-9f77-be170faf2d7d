<template>
  <div class="learning-logs-table-container">
    <div class="container-header">
      <div class="left">
        <div class="filter-row">
          <el-input
            v-model="queryParams.name"
            placeholder="请输入名称"
            clearable
            size="large"
          />
        </div>
        <div class="btn primary-btn" @click="handleQuery">
          <i-ep-search /> 搜索
        </div>
        <!-- <div class="filter-row">
          <el-cascader
            v-model="queryParams.dptid"
            :props="depProps"
            :options="depOptions"
            placeholder="请选择部门"
            clearable
            filterable
            size="large"
            @change="handleQuery"
            :suffix-icon="`CaretBottom`"
          />
        </div> -->
      </div>
    </div>
    <div class="content">
      <el-table
        v-loading="loading"
        element-loading-text="Loading"
        element-loading-background="#ffffffb4"
        :data="tableData"
        height="100%"
        border
        fit
        highlight-current-row
      >
        <el-table-column label="序号" align="center" min-width="20">
          <template #default="scope">
            {{ scope.$index + 1 }}
          </template>
        </el-table-column>
        <el-table-column label="用户名" align="center" min-width="40">
          <template #default="scope">
            <span>{{ scope.row.gk_user.name || "--" }}</span>
          </template>
        </el-table-column>

        <el-table-column label="学习进度" align="center" min-width="60">
          <template #default="scope">
            <div class="progress">
              <div class="line">
                <div class="progress-line">
                  <div
                    class="progress-bar"
                    :class="[
                      'free-bar',
                      scope.row.progress * 1 == 100 ? 'full-bar' : '',
                    ]"
                    :style="{
                      width: (scope.row.progress * 1 || 0) + '%',
                    }"
                  ></div>
                </div>
                <span style="color: #2ab7b0">
                  {{ (scope.row.progress || 0) + "%" }}
                </span>
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="开始学习日期" align="center" min-width="50">
          <template #default="scope">
            {{ scope.row.begin_at || "--" }}
          </template>
        </el-table-column>

        <el-table-column label="操作" align="center" min-width="40">
          <template #default="scope">
            <div class="option-btn">
              <div
                class="btn green-btn"
                @click="onRowClick('detail', scope.row)"
              >
                明细
              </div>
            </div>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <div class="footer">
      <pagination
        v-if="total > 0"
        v-model:total="total"
        v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize"
        @pagination="getData"
      />
    </div>

    <el-dialog
      class="learning-log-dialog"
      v-model="dialog.visible"
      :title="dialog.title"
      :width="dialog.width"
      append-to-body
      @close="closeDialog"
    >
      <template #header>
        <div class="dialog-title">
          {{ dialog.title }}
        </div>
      </template>
      <div class="dialog-body">
        <div class="log-tabs" ref="logTabs">
          <div
            v-for="item in logTabOptions"
            :key="item.value"
            :class="[
              'tab-item',
              item.value,
              { active: activeLogTab === item.value },
            ]"
            @click="handleLogTab(item.value)"
          >
            {{ item.label }}
          </div>
        </div>
        <div class="table-content">
          <el-table
            v-if="activeLogTab === 'course'"
            :data="learningData"
            height="100%"
            border
            fit
            :highlight-current-row="false"
            default-expand-all
            row-key="id"
            :span-method="courseSpanMethod"
          >
            <el-table-column label="名称" align="center" min-width="80">
              <template #default="scope">
                <div class="name">
                  {{ scope.row?.title }}
                </div>
              </template>
            </el-table-column>

            <el-table-column label="最后观看日期" align="center" min-width="40">
              <template #default="scope">
                <span>
                  {{ scope.row?.watched_at || "--" }}
                </span>
              </template>
            </el-table-column>
            <el-table-column label="进度" align="center" min-width="30">
              <template #default="scope">
                <span>
                  {{ scope.row?.progress ? scope.row.progress + "%" : "--" }}
                </span>
              </template>
            </el-table-column>
          </el-table>
          <el-table
            v-if="activeLogTab === 'exam'"
            :data="examData"
            height="100%"
            border
            fit
          >
            <el-table-column label="考试名称" align="center" min-width="80">
              <template #default="scope">
                {{ scope.row?.name || "--" }}
              </template>
            </el-table-column>
            <el-table-column label="完成日期" align="center" min-width="40">
              <template #default="scope">
                {{ scope.row.taken_at }}
              </template>
            </el-table-column>
            <el-table-column label="成绩" align="center" min-width="30">
              <template #default="scope">
                {{ scope.row.total_scores || "--" }}
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <div class="btn primary-btn" @click="closeDialog">确 定</div>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { useRoute, useRouter } from "vue-router";
import {
  getCourseLearnRecords,
  getCourseLearnRecordsDetail,
  getCourseExamRecordsById,
} from "@/api/course";

import { parseTime, numberToChinese, containsAllElements } from "@/utils";
import { useUserStore } from "@/store/modules/user";
import { checkUserPermission } from "@/utils/auth";

import * as XLSX from "xlsx";
import { saveAs } from "file-saver";
import { Progress } from "xgplayer";

//const { proxy } = getCurrentInstance();
// proxy 替代 this
defineOptions({
  name: "LearningLogs",
  inheritAttrs: false,
});

/**路由对象*/
const route = useRoute();
/**路由实例*/
const router = useRouter();

const props = defineProps({
  courseId: {
    type: String,
    default: "",
  },
  courseDetail: {
    type: Object,
    default: () => {},
  },
});
const loading = ref(false);

const queryParams = reactive<any>({
  name: "",
  pageNum: 1,
  pageSize: 20,
});

const courseId = ref<any>(props.courseId);
const courseDetail = reactive<any>(props.courseDetail);
const total = ref(0); // 数据总数
const tableData = ref<any>([]);

const dialog = reactive<any>({
  visible: false,
  title: "学习明细",
  width: "40%",
  type: "",
});
const logTabOptions = ref<any>([
  { value: "course", label: "课程学习" },
  { value: "exam", label: "考试记录" },
]);
const activeLogTab = ref<any>("course");
const rowId = ref<any>(null);
const learningData = ref<any>([]);
const examData = ref<any>([]);

onBeforeMount(() => {});
onMounted(() => {
  getData();
});

function optionsLoop(val: any) {
  const res = {
    label: val.name,
    value: val.id,
    children: val.children?.map(optionsLoop) ?? [],
  };
  return res;
}
function getData() {
  loading.value = true;
  const params = {
    name: queryParams.name || undefined,
    page: queryParams.pageNum,
    per_page: queryParams.pageSize,
  };
  getCourseLearnRecords(courseId.value, params)
    .then((res: any) => {
      tableData.value = res.data.learn_records.map((item: any) => {
        item.begin_at = item.begin_at
          ? parseTime(item.begin_at, "{y}-{m}-{d} {h}:{i}:{s}")
          : "--";
        return item;
      });
      total.value = res.total;
    })
    .finally(() => {
      loading.value = false;
    });
}
function handleQuery() {
  queryParams.pageNum = 1;
  getData();
}

function onRowClick(type: string, row: any) {
  if (type == "detail") {
    rowId.value = row.id;
    dialog.visible = true;
    dialog.title = row.gk_user?.name + "学习明细";

    getCourseLearnRecordsDetail(courseId.value, rowId.value).then(
      (res: any) => {
        learningData.value = res.data.record.map((item: any, index: any) => {
          let res: any = {
            title: "第" + numberToChinese(index) + "章：" + item.title,
            id: index,
          };
          if (item.videos?.length && item.videos?.length > 0) {
            res.children = item.videos.map((itm: any) => {
              return {
                watched_at: itm.watched_record?.watched_at
                  ? parseTime(
                      itm.watched_record?.watched_at,
                      "{y}-{m}-{d} {h}:{i}:{s}"
                    )
                  : "--",
                title: itm.resource?.name,
                progress: itm.watched_record?.progress || "",
              };
            });
          } else {
            Object.assign(res, {
              title: item.name || item.resource?.name,
              id: index,
              watched_at: item.watched_record?.watched_at
                ? parseTime(
                    item.watched_record?.watched_at,
                    "{y}-{m}-{d} {h}:{i}:{s}"
                  )
                : "--",
              progress: item.watched_record?.progress || "",
            });
          }
          return res;
        });
      }
    );

    getCourseExamRecordsById(courseId.value, {
      gk_user_id: row.gk_user.id,
    }).then((res: any) => {
      const mergedHistories: any[] = [];
      res.data.user_exams.forEach((item: any) => {
        if (Array.isArray(item.histories)) {
          const merge = item.histories.map((history: any) => {
            history.exam = item.exam;
            return history;
          });

          mergedHistories.push(...merge);
        }
      });
      examData.value = mergedHistories.map((item: any) => {
        let res: any = {
          name: item.exam.name,
          taken_at: item.taken_at
            ? parseTime(item.taken_at, "{y}-{m}-{d} {h}:{i}:{s}")
            : "--",
          title: item.exam?.name || item.exam?.title,
          total_scores: item.total_scores || "--",
        };
        return res;
      });
    });
  }
}

// 合并父级行单元格，仅保留名称列
function courseSpanMethod({ row, column, rowIndex, columnIndex }: any) {
  // 父级行：没有 watched_at 和 progress 字段
  const isParent = !row.watched_at && !row.progress;
  if (isParent) {
    if (columnIndex === 0) {
      // 名称列合并3列
      return [1, 3];
    } else {
      // 其它列隐藏
      return [0, 0];
    }
  }
  return [1, 1];
}

function expiredAtFilter(value: any) {
  let res: any = "--";

  if (value.package?.expired_at) {
    res = parseTime(value.package.expired_at, "{y}-{m}-{d}");
  }
  return res;
}
function closeDialog() {
  dialog.visible = false;
  activeLogTab.value = "course";
}

function handleLogTab(tab: any) {
  activeLogTab.value = tab;
}
</script>

<style scoped lang="scss">
.learning-logs-table-container {
  width: 100%;
  height: 100%;

  .container-header {
    display: flex;
    justify-content: space-between;
    width: 100%;
    padding: 20px;

    .left {
      display: flex;
      align-items: center;
      justify-content: space-between;
      // width: 60%;
      .btn {
        width: 116px;
        height: 40px;
        margin-left: 20px;
        border-radius: 2px;
      }
    }

    .right {
      display: flex;
      align-items: center;
      justify-content: center;

      .export-btn {
        width: 116px;
        height: 40px;
        background: linear-gradient(180deg, #0ebc72 0%, #20c27c 100%);
        border-radius: 2px;
      }
    }

    .filter-row {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 260px;
      margin-left: 20px;

      &:nth-child(1) {
        width: 260px;
        margin-left: 0;
      }
    }
  }

  .content {
    width: 100%;
    height: calc(100% - 120px);
    padding: 10px 20px;

    :deep(.caret-wrapper) {
      transform: scale(1.5) !important;
    }

    .progress {
      display: flex;
      flex-direction: column;
      align-items: center;
      // justify-content: space-between;

      .line {
        display: flex;
        align-items: center;
        justify-content: space-between;
        width: 190px;
        margin-bottom: 10px;
      }
    }

    .progress-line {
      width: 146px;
      height: 9px;
      margin-right: 10px;
      background: #7072787b;
      border-radius: 6px;
      box-shadow: 0 0 2px 1px rgb(55 134 104 / 16%),
        inset 0 0 4px 1px rgb(0 0 0 / 7%);

      .progress-bar {
        width: 146px;
        height: 9px;
        border-radius: 6px 0 0 6px;
      }

      .full-bar {
        width: 146px;
        height: 9px;
        border-radius: 6px;
      }

      .free-bar {
        width: 146px;
        height: 9px;
        background: linear-gradient(180deg, #33cec9 0%, #1c8d84 100%);
      }
    }
  }

  .footer {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-top: 20px;
  }
}
</style>
<style lang="scss">
.learning-log-dialog {
  padding: 0 !important;

  .el-dialog__header {
    height: 88px;
    text-align: center;
    background: url("@/assets/images/dialog-header-green.png") no-repeat;
    background-size: 100% 100%;

    .dialog-title {
      display: flex;
      align-items: center;
      justify-content: center;
      height: 100%;
      font-size: 24px;
      font-weight: 500;
      color: #fff;
    }
  }

  .el-dialog__body {
    padding: 20px !important;
  }

  .el-dialog__footer {
    padding: 20px !important;
  }

  .el-dialog__close {
    width: 21px;
    height: 21px;
    background: #fff;
    border-radius: 50%;

    svg {
      font-size: 20px;

      path {
        fill: #00918c !important;
      }
    }
  }

  .log-tabs {
    display: flex;
    width: 100%;
    height: 40px;
    overflow: hidden;
    background: #eaf2fa;
    border-radius: 4px 4px 0 0;

    .tab-item {
      display: flex;
      flex: 1;
      align-items: center;
      justify-content: center;
      font-size: 15px;
      font-weight: 400;
      color: #3b4664;
      cursor: pointer;
      background: linear-gradient(180deg, #cbccce 0%, #e7ecf0 100%);
      border-right: 1px solid #fff;
      transition: background 0.2s, color 0.2s;

      &:last-child {
        border-right: none;
      }
    }

    .tab-item.active {
      color: #fff;
      background: #00918c;
    }
  }

  .table-content {
    height: 20rem;

    .el-table__row--level-0 {
      background: #effada !important;

      .el-table__expand-icon {
        display: none;
      }

      .cell {
        display: flex !important;
        justify-content: flex-start;
        padding-left: 40px !important;
      }
    }

    .el-table__row--level-1 {
      .name {
        display: flex;
        align-items: center;
        width: 100%;
        height: 100%;
        // padding-left: 40px;
      }

      .cell {
        display: flex !important;
        justify-content: center;
      }
    }

    .el-table__row--level-1:hover {
      background: linear-gradient(180deg, #fafdfc 0%, #eafaf0 100%) !important;
    }

    tbody tr:hover > td {
      background: none !important;
    }
  }
}
</style>
