<template>
  <div class="agent-channel-container">
    <div class="container-header">
      <div class="left">
        <div class="filter-row">
          <el-input
            v-model="queryParams.search"
            placeholder="请输入关键字"
            clearable
            size="large"
          />
        </div>
        <div class="btn primary-btn" @click="handleQuery">
          <i-ep-search /> 搜索
        </div>
      </div>
      <div class="right">
        <div class="btn text-btn" @click="handleCreate">生成渠道码</div>
      </div>
    </div>
    <div class="content">
      <el-table
        v-loading="loading"
        element-loading-text="Loading"
        element-loading-background="#ffffffb4"
        :data="tableData"
        height="100%"
        border
        fit
        highlight-current-row
      >
        <el-table-column label="序号" align="center" min-width="40">
          <template #default="scope">
            {{ scope.$index + 1 }}
          </template>
        </el-table-column>
        <el-table-column label="团队名称" align="center" min-width="120">
          <template #default="scope">
            {{ scope.row.name }}
          </template>
        </el-table-column>

        <el-table-column label="联系人" align="center" min-width="80">
          <template #default="scope">
            {{ scope.row.contact_person }}
          </template>
        </el-table-column>
        <el-table-column label="手机号码" align="center" min-width="120">
          <template #default="scope">
            {{ scope.row.mobile }}
          </template>
        </el-table-column>
        <el-table-column label="状态" align="center" min-width="50">
          <template #default="scope">
            <el-tag :type="statusMap[scope.row.status]?.type">{{
              statusMap[scope.row.status]?.label
            }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="创建时间" align="center" min-width="120">
          <template #default="scope">
            {{ scope.row.created_at }}
          </template>
        </el-table-column>

        <el-table-column label="操作" align="center" min-width="120">
          <template #default="scope">
            <div class="option-btn">
              <div
                class="btn primary-btn"
                v-if="scope.row.status != 20"
                @click="onRowClick('detail', scope.row)"
              >
                查看
              </div>
              <div
                class="btn light-green-btn2"
                @click="onRowClick('data', scope.row)"
              >
                数据
              </div>
              <div
                class="btn delete-btn"
                v-if="scope.row.status != 20"
                @click="onRowClick('delete', scope.row)"
              >
                删除
              </div>
            </div>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <div class="footer">
      <pagination
        v-if="total > 0"
        v-model:total="total"
        v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize"
        @pagination="getData"
      />
    </div>

    <el-dialog
      class="channelDialog"
      v-model="channelDialog.visible"
      append-to-body
      :width="channelDialog.width"
      :title="channelDialog.title"
      @close="closeDialog"
    >
      <div class="dialog-body">
        <div
          class="qr-img"
          v-if="channelForm.code_url && channelDialog.type == 'detail'"
        >
          <VueQrcode
            :value="channelForm.code_url"
            :size="70"
            :type="'image/png'"
            :color="{ dark: '#000', light: '#fff' }"
          />
          <div class="text">
            链接：
            <span>{{ channelForm.redirect_url }}</span>
          </div>
        </div>
        <div class="form-content" v-else>
          <el-form
            label-width="6rem"
            :model="channelForm"
            :rules="formDataRules"
            ref="formRef"
          >
            <el-form-item label="团队名称" prop="name">
              <el-input
                v-model="channelForm.name"
                placeholder="请输入团队名称"
                clearable
                size="large"
              />
            </el-form-item>

            <el-form-item label="联系人" prop="contact_person">
              <el-input
                v-model="channelForm.contact_person"
                placeholder="请输入联系人"
                clearable
                size="large"
              />
            </el-form-item>
            <el-form-item label="手机号码" prop="mobile">
              <el-input
                v-model="channelForm.mobile"
                placeholder="请输入手机号码"
                clearable
                size="large"
              />
            </el-form-item>
          </el-form>
        </div>
      </div>
      <template #footer>
        <div
          class="dialog-footer footer1"
          v-if="channelForm.code_url && channelDialog.type == 'detail'"
        >
          <div class="btn primary-btn" @click="handleCopy">复制链接</div>
          <div class="btn primary-btn" @click="handleDownload">下载二维码</div>
          <div class="btn primary-btn" @click="closeDialog">确定</div>
        </div>
        <div class="dialog-footer" v-else>
          <div class="btn cancel-btn" @click="closeDialog">取消</div>
          <div class="btn primary-btn" @click="handleSubmit">立即生成</div>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import VueQrcode from "vue-qrcode";
import { useAppStore } from "@/store/modules/app";
import { useRoute, useRouter } from "vue-router";
import {
  getAgentChannels,
  getAgentChannelDetail,
  addAgentChannel,
  deleteAgentChannel,
  updateAgentChannel,
} from "@/api/promotion";
import {
  parseTime,
  secondsToHoursAndMinutes,
  resetReactiveObject,
} from "@/utils";
//const { proxy } = getCurrentInstance();
// proxy 替代 this
defineOptions({
  name: "AgentChannel",
  inheritAttrs: false,
});
const store = useAppStore();
const route = useRoute();
const router = useRouter();

const loading = ref(false);

const queryParams = reactive<any>({
  search: "",
  pageNum: 1,
  pageSize: 20,
});
const total = ref(0); // 数据总数
const tableData = ref([]);

const statusMap = reactive<any>({
  10: { type: "success", label: "正常", color: "#00918c" },
  20: { type: "danger", label: "删除", color: "#f56c6c" },
});
const channelDialog = reactive<any>({
  visible: false,
  title: "生成渠道码",
  width: "28%",
  type: "detail",
});
const channelForm = reactive<any>({
  name: "",
  description: "",
  mobile: "",
  contact_person: "",
  // code_url: "https://www.baidu.com",
});
const formRef = ref(ElForm); // 表单ref
const formDataRules = reactive<any>({
  name: [{ required: true, message: "请输入渠道名称", trigger: "blur" }],
  mobile: [{ required: true, message: "请输入手机号码", trigger: "blur" }],
  contact_person: [
    { required: true, message: "请输入联系人", trigger: "blur" },
  ],
});
const env_url: any = import.meta.env.VITE_APP_API_URL;
onBeforeMount(() => {});
onMounted(() => {
  getData();
});

function getData() {
  loading.value = true;
  const params = {
    search: queryParams.search || undefined,
    page: queryParams.pageNum,
    per_page: queryParams.pageSize,
  };
  getAgentChannels(params).then((res: any) => {
    tableData.value = res.data.channel_code.map((item: any) => {
      item.created_at = parseTime(item.created_at, "{y}-{m}-{d} {h}:{i}:{s}");
      return item;
    });
    total.value = res.total;
    loading.value = false;
  });
}

function handleCreate() {
  channelDialog.visible = true;
  channelDialog.type = "create";
  channelDialog.title = "生成渠道码";
}
function onRowClick(type: any, row: any) {
  if (type == "detail") {
    channelDialog.visible = true;
    channelDialog.title = "查看推广码";
    channelDialog.type = "detail";
    channelForm.code_url = `https://gknowledge.cn/promotion_code?channel_code_id=${row.id}`;
    channelForm.redirect_url = `https://gknowledge.cn/#/promotion/index?channel_code_id=${row.id}`;
  }
  if (type == "data") {
    router.push({
      path: "agent-channel-detail",
      query: { id: row.id, type: "detail" },
    });
  }
  if (type == "delete") {
    handelDelete(row);
  }
}
function handleSubmit() {
  const data = {
    name: channelForm.name,
    description: channelForm.description,
    mobile: channelForm.mobile,
    contact_person: channelForm.contact_person,
  };
  formRef.value.validate((valid: any, fields: any) => {
    if (valid) {
      addAgentChannel(data).then((res: any) => {
        if (res.status == 200) {
          ElMessage.success({
            message: `生成成功!`,
          });
          closeDialog();
          getData();
        }
      });
    }
  });
}

function handleCopy() {
  const text = channelForm.redirect_url;
  navigator.clipboard.writeText(text).then(() => {
    ElMessage.success({
      message: "复制成功!",
    });
  });
}

function handleDownload() {
  const img: any = document.querySelector(".qr-img img");
  const url = img.src;
  const a = document.createElement("a");
  a.href = url;
  a.download = "推广二维码.png";
  a.click();
}
function closeDialog() {
  channelDialog.visible = false;
  setTimeout(() => {
    channelForm.code_url = "";
    channelForm.name = "";
    channelForm.description = "";
    channelForm.mobile = "";
    channelForm.contact = "";
  }, 200);
}
function handelDelete(row: any) {
  ElMessageBox.confirm(`此操作将删除代理渠道，是否继续?`, "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  }).then(() => {
    deleteAgentChannel(row.id).then((res: any) => {
      if (res.status == 200) {
        ElMessage.success({
          message: `删除成功!`,
        });
        getData();
      }
    });
  });
}
function handleQuery() {
  queryParams.pageNum = 1;
  getData();
}
</script>

<style scoped lang="scss">
.agent-channel-container {
  // width: 50%;
  height: 95%;
  margin: 20px;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 3px 20px 1px rgb(109 125 126 / 7%);

  .container-header {
    display: flex;
    justify-content: space-between;
    width: 100%;
    padding: 20px;

    .left {
      display: flex;
      align-items: center;
      justify-content: space-between;
      // width: 60%;
      .btn {
        width: 116px;
        height: 40px;
        margin-left: 20px;
      }
    }

    .right {
      display: flex;
      align-items: center;
      justify-content: center;

      .btn {
        width: 154px;
        height: 42px;
        color: #fff;
        background: linear-gradient(180deg, #00918c 0%, #1c8d84 100%);
        border-radius: 10px;
        box-shadow: 0 3px 10px 1px rgb(29 162 131 / 25%),
          inset 0 3px 10px 1px rgb(193 239 233 / 40%);
      }
    }

    .filter-row {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 260px;
      margin-left: 20px;

      &:nth-child(1) {
        width: 260px;
        margin-left: 0;
      }

      .btn {
        width: 116px;
        height: 40px;
        margin-left: 20px;
      }
    }
  }

  .content {
    width: 100%;
    height: calc(100% - 170px);
    padding: 10px 20px;

    .cover-name {
      img {
        object-fit: cover;
      }
    }

    .option-btn {
      display: flex;
      align-items: center;
      justify-content: flex-start !important;
    }
  }

  .footer {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-top: 15px;
  }
}
</style>
<style lang="scss">
.channelDialog {
  .dialog-body {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
  }

  .qr-img {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    width: 100%;
    margin-bottom: 10px;
  }

  .text {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    padding: 10px 0;
    background: #edeff4;
    border-radius: 8px;
    box-shadow: inset 3px 3px 6px 1px rgb(188 198 214 / 14%);
  }

  .form-content {
    width: 100%;
  }

  .footer1 {
    display: flex;
    align-items: center !important;
    justify-content: center !important;
  }
}
</style>
