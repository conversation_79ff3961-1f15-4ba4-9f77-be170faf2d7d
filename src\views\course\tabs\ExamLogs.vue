<template>
  <div class="exam-logs-table-container">
    <div class="container-header">
      <div class="left">
        <div class="filter-row">
          <el-input
            v-model="queryParams.name"
            placeholder="请输入用户名"
            clearable
            size="large"
          />
        </div>

        <div class="btn primary-btn" @click="handleQuery">
          <i-ep-search /> 搜索
        </div>
        <div class="filter-row">
          <el-select
            size="large"
            v-model="queryParams.exam_id"
            placeholder="请选择考试"
            filterable
            clearable
            :suffix-icon="`CaretBottom`"
            @change="handleQuery"
          >
            <el-option
              v-for="item in examOptions"
              :key="item.value"
              :label="item.label"
              :value="item.id"
            />
          </el-select>
        </div>
        <div class="filter-row">
          <el-select
            size="large"
            v-model="queryParams.grade"
            placeholder="请选择等级"
            filterable
            clearable
            :suffix-icon="`CaretBottom`"
            @change="handleQuery"
          >
            <el-option
              v-for="item in gradesOptions"
              :key="item.value"
              :label="item.label"
              :value="item.label"
            />
          </el-select>
        </div>
        <!-- <div class="filter-row">
          <el-select
            size="large"
            v-model="queryParams.status"
            placeholder="请选择状态"
            filterable
            clearable
            :suffix-icon="`CaretBottom`"
            @change="handleQuery"
          >
            <el-option
              v-for="item in examStatusOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </div> -->
      </div>
    </div>
    <div class="content">
      <el-table
        v-loading="loading"
        element-loading-text="Loading"
        element-loading-background="#ffffffb4"
        :data="tableData"
        height="100%"
        border
        fit
        highlight-current-row
      >
        <el-table-column label="序号" align="center" min-width="25">
          <template #default="scope">
            {{ scope.$index + 1 }}
          </template>
        </el-table-column>
        <el-table-column label="用户名" align="center" min-width="50">
          <template #default="scope">
            <span>{{ scope.row.gk_user?.name || "--" }}</span>
          </template>
        </el-table-column>
        <el-table-column label="考试名称" align="center" min-width="50">
          <template #default="scope">
            <span>{{ scope.row.exam?.name || "--" }}</span>
          </template>
        </el-table-column>
        <el-table-column label="考试时间" align="center" min-width="50">
          <template #default="scope">
            {{ scope.row.taken_at || "--" }}
          </template>
        </el-table-column>
        <el-table-column label="成绩" align="center" min-width="30">
          <template #default="scope">
            <span>{{
              scope.row.total_scores != null ? scope.row.total_scores : "--"
            }}</span>
          </template>
        </el-table-column>
        <el-table-column label="考试次数" align="center" min-width="30">
          <template #default="scope">
            <span style="color: #00918c">{{ scope.row.result_num }}</span>
            /
            <span>{{ scope.row.exam.try_times }}</span>
          </template>
        </el-table-column>
        <el-table-column label="等级" align="center" min-width="30">
          <template #default="scope">
            <span :style="{ color: gradesMap[scope.row.grade]?.color }">
              {{ scope.row.grade || "--" }}</span
            >
          </template>
        </el-table-column>
        <el-table-column label="状态" align="center" min-width="30">
          <template #default="scope">
            <el-tag :type="examStatusMap[scope.row.status]?.type">
              {{ examStatusMap[scope.row.status]?.label }}
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column label="完成时间" align="center" min-width="50">
          <template #default="scope">
            {{ scope.row.submit_at || "--" }}
          </template>
        </el-table-column>

        <el-table-column label="操作" align="center" min-width="50">
          <template #default="scope">
            <div class="option-btn">
              <div
                v-if="scope.row.status == 20"
                class="btn green-btn"
                @click="onRowClick('detail', scope.row)"
              >
                详情
              </div>
              <div
                v-if="scope.row.status == 20"
                class="btn orange-btn"
                @click="onRowClick('reset', scope.row)"
              >
                重置
              </div>
            </div>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <div class="footer">
      <pagination
        v-if="total > 0"
        v-model:total="total"
        v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize"
        @pagination="getData"
      />
    </div>

    <el-dialog
      class="exam-log-dialog"
      v-model="dialog.visible"
      v-if="dialog.visible"
      :title="dialog.title"
      :width="dialog.width"
      append-to-body
      @close="closeDialog"
    >
      <template #header>
        <div class="dialog-title" :class="{ 'has-bg': dialog.type == 'reset' }">
          {{ dialog.title }}
        </div>
      </template>
      <div class="dialog-body">
        <div class="exam-content" v-if="dialog.type == 'detail'">
          <el-scrollbar
            class="exam-detail-scrollbar"
            style="width: 100%; height: 100%"
            warp-style="overflow-x: hidden;"
          >
            <div class="exam-text">
              <div class="left">
                {{ examDetail.exam?.remark }}
              </div>
              <div class="right">
                <span
                  >共 {{ examDetail.questions?.length || 0 }} 题 | 总分
                  {{ examDetail.exam?.score_set }} 分 | 限时
                  {{ examDetail.exam?.time_limit }} 分钟</span
                >
              </div>
            </div>
            <div class="exam-info">
              <div class="score-info">
                <div
                  class="score-content"
                  :class="{
                    'score-bg-pass':
                      examDetail.total_scores >= examDetail.pass_scores,
                    'score-bg-fail':
                      examDetail.total_scores < examDetail.pass_scores,
                  }"
                >
                  <div
                    class="score-item num-font-type"
                    :class="{
                      correct:
                        examDetail.total_scores >= examDetail.pass_scores,
                      wrong: examDetail.total_scores < examDetail.pass_scores,
                    }"
                  >
                    {{ examDetail.total_scores }}
                  </div>
                  <div
                    class="score-tag"
                    :class="{
                      'pass-tag':
                        examDetail.total_scores >= examDetail.pass_scores,
                      'fail-tag':
                        examDetail.total_scores < examDetail.pass_scores,
                    }"
                  >
                    卷面分数 {{ examDetail.exam?.score_set }}
                  </div>
                </div>
              </div>
              <div class="paper-info">
                <div class="paper-time">
                  <div class="item">
                    <span>考试时间：{{ examDetail.taken_at }}</span>
                  </div>
                  <div class="item">
                    <span
                      >考试时长：{{
                        getExamDuration(
                          examDetail.taken_at,
                          examDetail.submit_at
                        )
                      }}</span
                    >
                  </div>
                  <div class="item">
                    <span>交卷时间：{{ examDetail.submit_at }}</span>
                  </div>
                </div>
                <div class="paper-answer">
                  <div class="item">
                    <span> 正确</span>
                    <span class="correct num-font-type">
                      {{ examDetail.answer_stat?.correct || 0 }}</span
                    >
                  </div>
                  <div class="line"></div>
                  <div class="item">
                    <span> 错误</span>
                    <span class="wrong num-font-type">
                      {{ examDetail.answer_stat?.wrong || 0 }}</span
                    >
                  </div>
                </div>
              </div>
              <div class="level-tag">
                <img :src="gradesMap[examDetail.grade]?.img" alt="" />
              </div>
            </div>
            <!-- <div class="exam-questions-page">
              
              <el-pagination
                background
                layout="prev, pager, next"
                :current-page="questionCurrentPage"
                :page-size="questionPageSize"
                :total="questionTotal"
                @current-change="handleQuestionPageChange"
              />
            </div> -->
            <div class="exam-questions">
              <div class="question-tabs" ref="questionTabs">
                <div
                  v-for="item in questionTabOptions"
                  :key="item.value"
                  :class="[
                    'tab-item',
                    item.value,
                    { active: activeQuestionTab === item.value },
                  ]"
                  @click="handleQuestionTab(item.value)"
                >
                  {{ item.label }}
                  {{
                    item.value === "all"
                      ? `（${item.count}）`
                      : `：${item.count}`
                  }}
                </div>
              </div>
              <div class="question-content">
                <el-scrollbar
                  style="width: 100%; height: 100%; overflow-x: hidden"
                >
                  <ExamPaper
                    :isOperable="false"
                    :showAnswerType="activeQuestionTab"
                    :showAnalysis="true"
                    :showAllAnswer="true"
                    :showSelectedAnswer="true"
                    :showQuestionScore="true"
                    :showGetScore="true"
                    :showAnswerTime="true"
                    :paperData="examDetail.questions"
                  />
                </el-scrollbar>
              </div>
            </div>
          </el-scrollbar>
        </div>
        <div class="reset-content" v-if="dialog.type == 'reset'">
          <div class="text">
            重置后将清除学员当前成绩和考试记录，请确认是否重置？
          </div>
        </div>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <div
            class="btn cancel-btn"
            @click="closeDialog"
            v-if="dialog.type == 'reset'"
          >
            取 消
          </div>
          <div class="btn primary-btn" @click="confirmDialog">确 定</div>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { useRoute, useRouter } from "vue-router";

import { parseTime, numberToChinese, containsAllElements } from "@/utils";
import { useUserStore } from "@/store/modules/user";
import { checkUserPermission } from "@/utils/auth";
import ExamPaper from "@/views/exam/components/ExamPaper.vue";
import {
  getExams,
  getUserExams,
  resetUserExam,
  getUserExamDetail,
} from "@/api/exam";
import { getCourseExamRecordsById, getExamRecordDetail } from "@/api/course";
import * as XLSX from "xlsx";
import { saveAs } from "file-saver";

//const { proxy } = getCurrentInstance();
// proxy 替代 this
defineOptions({
  name: "ExamLogs",
  inheritAttrs: false,
});

/**路由对象*/
const route = useRoute();
/**路由实例*/
const router = useRouter();

const props = defineProps({
  courseId: {
    type: String,
    default: "",
  },
  courseDetail: {
    type: Object,
    default: () => {},
  },
});
const loading = ref(false);

const queryParams = reactive<any>({
  search: "",
  pageNum: 1,
  pageSize: 20,
  status: null,
});

const courseId = ref<any>(props.courseId);
const courseDetail = reactive<any>(props.courseDetail);
const total = ref(0); // 数据总数
const tableData = ref<any>([]);

const gradesMap = reactive<any>({
  // 不及格，及格，良好，优秀
  及格: {
    label: "及格",
    color: "#7EBFAE",
    img: new URL("@/assets/exam/pass-tag.png", import.meta.url).href,
  },
  良好: {
    label: "良好",
    color: "#2ABF7E",
    img: new URL("@/assets/exam/good-tag.png", import.meta.url).href,
  },
  优秀: {
    label: "优秀",
    color: "#23A19A",
    img: new URL("@/assets/exam/excellent-tag.png", import.meta.url).href,
  },
  不及格: {
    label: "不及格",
    color: "#EA844F",
    img: new URL("@/assets/exam/fail-tag2.png", import.meta.url).href,
  },
  未完成: {
    label: "未完成",
    color: "#3B4664",
    img: new URL("@/assets/exam/undone-tag.png", import.meta.url).href,
  },
});
const examOptions = ref<any>([]);
const gradesOptions = ref<any>([
  { value: 10, label: "及格", en: "pass" },
  { value: 20, label: "良好", en: "good" },
  { value: 30, label: "优秀", en: "excellent" },
  { value: 40, label: "不及格", en: "fail" },
  { value: 50, label: "未完成", en: "undone" },
]);
// 考试状态，10-进行中，20-完成，30-超时，40-待移除
const examStatusOptions = ref<any>([
  { value: "10", label: "进行中" },
  { value: "20", label: "已完成" },
  { value: "30", label: "超时" },
  // { value: "40", label: "待移除" },
]);
const examStatusMap = reactive<any>({
  10: { type: "primary", label: "进行中", color: "#409eff" },
  20: { type: "success", label: "已完成", color: "#2ab7b0" },
  30: { type: "warning", label: "超时", color: "#e6a23c" },
  40: { type: "danger", label: "已废弃", color: "#f56c6c" },
});
// 课程性质
const courseTypeOptions = ref<any>([
  { value: "1", label: "必修" },
  { value: "0", label: "选修" },
]);
const courseTypeMap = reactive<any>({
  true: { type: "primary", label: "必修", color: "#409eff" },
  false: { type: "success", label: "选修", color: "#2ab7b0" },
});

//考试类型
const examTypeOptions = ref<any>([]);
const examTypeMap = reactive<any>({
  // true: { type: "primary", label: "--", color: "#409eff" },
  // false: { type: "success", label: "--", color: "#2ab7b0" },
});

const dialog = reactive<any>({
  visible: false,
  title: "学习明细",
  width: "40%",
  type: "",
});
const rowParams = reactive<any>({});
const examDetail = reactive<any>({});
const rowId = ref<any>(null);
const activeQuestionTab = ref<any>("all");
const questionTabOptions = ref<any>([
  {
    value: "all",
    label: "全部",
    count: 4,
  },
  {
    value: "correct",
    label: "正确",
    count: 3,
  },
  {
    value: "wrong",
    label: "错误",
    count: 1,
  },
]);
const questionCurrentPage = ref(1);
const questionPageSize = ref(10);
const questionTotal = ref(10);

function handleQuestionPageChange(page: number) {
  questionCurrentPage.value = page;
}

onBeforeMount(() => {});
onMounted(() => {
  getExamsData();
  getData();
});

function getExamsData() {
  loading.value = true;
  const params = {
    per_page: 9999,
    page: 1,
  };
  getExams(params).then((res: any) => {
    examOptions.value = res.data.exams.map((item: any) => {
      item.label = item.name;
      return item;
    });
  });
}
function getData() {
  loading.value = true;
  const params = {
    // cid: courseId.value,
    name: queryParams.name || undefined,
    page: queryParams.pageNum,
    per_page: queryParams.pageSize,
    grade: queryParams.grade || undefined,
    gk_exam_id: queryParams.exam_id || undefined,
  };
  getCourseExamRecordsById(courseId.value, params).then((res: any) => {
    tableData.value = res.data.user_exams.map((item: any) => {
      item.taken_at = item.taken_at
        ? parseTime(item.taken_at, "{y}-{m}-{d} {h}:{i}:{s}")
        : "";
      item.submit_at = item.submit_at
        ? parseTime(item.submit_at, "{y}-{m}-{d} {h}:{i}:{s}")
        : "";
      item.created_at = item.created_at
        ? parseTime(item.created_at, "{y}-{m}-{d} {h}:{i}:{s}")
        : "";
      item.result_num =
        item.histories?.filter((item) => item.grade).length || 0;
      return item;
    });

    total.value = res.total;
    loading.value = false;
  });
}
function optionsLoop(val: any) {
  const res = {
    label: val.name,
    value: val.id,
    children: val.children?.map(optionsLoop) ?? [],
  };
  return res;
}

// 计算花费时间时长
function getExamDuration(taken_at: string, submit_at: string) {
  if (!taken_at || !submit_at) return "--";
  const start = new Date(taken_at.replace(/-/g, "/"));
  const end = new Date(submit_at.replace(/-/g, "/"));
  let diff = Math.max(0, end.getTime() - start.getTime()) / 1000; // 秒
  const hour = Math.floor(diff / 3600);
  diff = diff % 3600;
  const min = Math.floor(diff / 60);
  const sec = Math.floor(diff % 60);
  // 补零函数
  const pad = (n: number) => n.toString().padStart(2, "0");

  // if (hour > 0) {
  //   return `${pad(hour)}:${pad(min)}:${pad(sec)}`;
  // }
  // return `${pad(min)}:${pad(sec)}`;
  return `${pad(hour)}:${pad(min)}:${pad(sec)}`;
}

// 获取考试结果等级和分数
function getResultGradeScore(type: any) {
  let res: any = 0;
  const needGrade = examDetail.result_grades?.filter((item) => {
    return Object.keys(item)[0] == type;
  });
  if (needGrade?.length) {
    res = Object.values(needGrade[0])[0];
  }
  return res;
}

function handleQuery() {
  queryParams.pageNum = 1;
  getData();
}

function onRowClick(type: string, row: any) {
  rowId.value = row.id;
  if (type == "detail") {
    dialog.visible = true;
    dialog.title = row.gk_user?.name + "考试记录";
    dialog.width = "40%";
    dialog.type = "detail";
    // getUserExamDetail(row.id)
    getExamRecordDetail(courseId.value, row.id, { is_ent: 0 }).then(
      (res: any) => {
        const data = res.data.user_exam;
        data.submit_at = data.submit_at
          ? parseTime(data.submit_at, "{y}-{m}-{d} {h}:{i}:{s}")
          : "";
        data.taken_at = data.taken_at
          ? parseTime(data.taken_at, "{y}-{m}-{d} {h}:{i}:{s}")
          : "";
        data.pass_scores = getResultGradeScore("及格");
        Object.assign(examDetail, data);
        questionTabOptions.value[0].count = examDetail.questions?.length || 0;
        questionTabOptions.value[1].count =
          examDetail.answer_stat?.correct || 0;
        questionTabOptions.value[2].count = examDetail.answer_stat?.wrong || 0;
      }
    );
  }
  if (type == "reset") {
    // 重置考试
    dialog.visible = true;
    dialog.title = "重置考试";
    dialog.width = "21.5%";
    dialog.type = "reset";
    Object.assign(rowParams, row);
  }
}
function handleResetExam() {
  const params = {
    is_ent: false,
    gk_user_id: rowParams.gk_user.id,
    exam_id: rowParams.exam.id,
  };
  resetUserExam(params).then((res: any) => {
    if (res.status == 200) {
      ElMessage.success({
        message: "重置成功!",
      });
      getData();
      closeDialog();
    }
  });
}

function handleQuestionTab(tab: any) {
  activeQuestionTab.value = tab;
}
function confirmDialog() {
  if (dialog.type == "reset") {
    handleResetExam();
  } else {
    closeDialog();
  }
}
function closeDialog() {
  dialog.visible = false;
  activeQuestionTab.value = "all";
}
</script>

<style scoped lang="scss">
.exam-logs-table-container {
  width: 100%;
  height: 100%;

  .container-header {
    display: flex;
    justify-content: space-between;
    width: 100%;
    padding: 20px;

    .left {
      display: flex;
      align-items: center;
      justify-content: space-between;

      .btn {
        width: 116px;
        height: 40px;
        margin-left: 20px;
        border-radius: 2px;
      }
    }

    .right {
      display: flex;
      align-items: center;
      justify-content: center;

      .export-btn {
        width: 116px;
        height: 40px;
        background: linear-gradient(180deg, #0ebc72 0%, #20c27c 100%);
        border-radius: 2px;
      }
    }

    .filter-row {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 260px;
      margin-left: 20px;

      &:nth-child(1) {
        width: 260px;
        margin-left: 0;
      }
    }
  }

  .content {
    width: 100%;
    height: calc(100% - 120px);
    padding: 10px 20px;

    :deep(.caret-wrapper) {
      transform: scale(1.5) !important;
    }

    .option-btn {
      display: flex !important;
      align-items: center !important;
      justify-content: flex-start !important;

      .btn {
        min-width: 65px;
      }
    }
  }

  .footer {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-top: 20px;
  }
}
</style>
<style lang="scss">
.exam-log-dialog {
  padding: 0 !important;

  .el-dialog__header.show-close {
    padding: 0 !important;
  }

  .el-dialog__header {
    height: 88px;
    padding: 10px !important;
    text-align: center;
    // background: url("@/assets/images/dialog-header-orange.png") no-repeat;
    // background-size: 100% 100%;

    .dialog-title {
      display: flex;
      align-items: center;
      justify-content: center;
      height: 100%;
      font-size: 20px;
      font-weight: 500;
      color: #3b4664;
    }

    .has-bg {
      background: url("@/assets/images/dialog-header-orange.png") no-repeat;
      background-size: 100% 100%;
    }
  }

  .el-dialog__body {
    padding: 0 20px !important;
  }

  .el-dialog__footer {
    padding: 20px !important;
  }

  .el-dialog__close {
    width: 21px;
    height: 21px;
    background: #fff;
    border-radius: 50%;

    svg {
      font-size: 20px;

      path {
        fill: #00918c !important;
      }
    }
  }

  .dialog-body {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
  }

  .reset-content {
    display: flex;
    align-items: center;
    justify-content: center;
    // text-align: center;
    padding: 0 20px;
    font-size: 18px;
    font-weight: 500;
    color: #3b4664;
  }

  .exam-content {
    width: 100%;
    // height: 65vh;

    .exam-text {
      display: flex;
      // align-items: center;
      justify-content: space-between;
      width: 100%;
      font-size: 16px;
      font-weight: 400;
      color: #3b4664;

      .left {
        width: 62%;
      }

      .right {
        width: 35%;
        text-align: right;
      }
    }

    .exam-info {
      position: relative;
      z-index: 1;
      display: flex;
      align-items: center;
      justify-content: space-around;
      width: 100%;
      height: 244px;
      margin: 10px 0;
      font-size: 16px;
      font-weight: 400;
      color: #3b4664;
      border: 1px solid #fff;
      border-radius: 8px;
      box-shadow: 0 0 6px 1px rgb(191 226 206 / 8%),
        inset 0 0 10px 1px rgb(19 69 65 / 20%);
    }

    .num-font-type {
      font-family: "Fugaz One", sans-serif !important;
    }

    .score-info {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      width: 35%;

      .score-content {
        position: relative;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        width: 185px;
        height: 187px;

        .score-item {
          // font-size: 80px;
          font-size: 70px;
          font-style: normal;
          font-weight: 400;
          // color: #00918c;
          line-height: 1;
          text-align: center;
          text-transform: none;
        }

        .score-tag {
          display: flex;
          align-items: center;
          justify-content: center;
          width: 115px;
          height: 25px;
          font-size: 14px;
          font-weight: 400;
          border-radius: 11px;
        }

        .pass-tag {
          color: #3b4664;
          background: linear-gradient(180deg, #c1edc1 0%, #7ebfae 100%);
        }

        .fail-tag {
          color: #fff;
          background: #f06e15;
        }
      }

      .score-bg-pass {
        background: url("@/assets/exam/score-pass-bg.png") no-repeat;
        background-size: 100% 100%;
      }

      .score-bg-fail {
        background: url("@/assets/exam/score-fail-bg.png") no-repeat;
        background-size: 100% 100%;
      }
    }

    .paper-info {
      display: flex;
      align-items: center;
      justify-content: space-around;
      width: 62%;
      height: 146px;

      .paper-time {
        display: flex;
        flex-direction: column;
        align-items: center;
        height: 100%;
        font-size: 16px;
        font-weight: 400;
        color: #3b4664;

        .item {
          position: relative;
          display: flex;
          align-items: center;
          width: 280px;
          height: 46px;
          padding: 0 10px;
          margin-bottom: 3px;
          background: #fff;
          border: 1px solid #fff;
          // 使用伪元素做背景并设置透明度，内容不受影响
          &::before {
            position: absolute;
            inset: 0;
            z-index: 0;
            pointer-events: none;
            content: "";
            background: linear-gradient(180deg, #eaf8e5 0%, #e2f8e2 100%);
            border-radius: 11px;
            box-shadow: 0 0 6px 1px rgb(191 226 206 / 8%),
              inset 0 0 10px 1px rgb(19 69 65 / 20%);
            opacity: 0.49;
          }

          > * {
            position: relative;
            z-index: 1;
          }
        }

        .item:last-child {
          margin-bottom: 0;
        }
      }

      .paper-answer {
        position: relative;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        height: 100%;

        // 使用伪元素做背景并设置透明度，内容不受影响
        &::before {
          position: absolute;
          inset: 0;
          z-index: 0;
          pointer-events: none;
          content: "";
          background: linear-gradient(180deg, #eaf8e5 0%, #e2f8e2 100%);
          border-radius: 11px;
          box-shadow: 0 0 6px 1px rgb(191 226 206 / 8%),
            inset 0 0 10px 1px rgb(19 69 65 / 20%);
          opacity: 0.49;
        }

        > * {
          position: relative;
          z-index: 1;
        }

        .line {
          width: calc(100% - 26px);
          height: 1px;
          margin: 5px 0;
          background: hsl(200deg 6% 42% / 44%);
        }

        .item {
          display: flex;
          flex: 1;
          align-items: center;
          justify-content: center;
          width: 144px;
          font-size: 17px;
          font-weight: bold;
          color: #3b4664;

          .num-font-type {
            margin-left: 10px;
            font-size: 40px;
            font-weight: 400;
          }
        }
      }
    }

    .correct {
      color: #00918c;
    }

    .wrong {
      color: #f06e15;
    }

    .level-tag {
      position: absolute;
      top: 0;
      right: 28px;
      width: 58px;
      height: 54px;

      img {
        width: 100%;
        height: 100%;
      }
    }

    .exam-questions-page {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 100%;
      padding-bottom: 10px;

      .el-pagination {
        margin: 0 auto;

        .el-pager li.is-active {
          color: #fff;
          background: #00918c;
          border-radius: 4px;
        }
      }
    }

    .exam-questions {
      display: flex;
      flex-direction: column;
      align-items: center;
      width: 100%;
      // height: 421px;
      height: 320px;
      border: 1px solid #fff;
      box-shadow: 0 0 6px 1px rgb(191 226 206 / 16%),
        inset 0 0 10px 1px rgb(19 69 65 / 20%);

      .question-tabs {
        display: flex;
        width: 100%;
        height: 40px;
        overflow: hidden;
        background: #eaf2fa;
        border-radius: 4px 4px 0 0;

        .tab-item {
          display: flex;
          flex: 1;
          align-items: center;
          justify-content: center;
          font-size: 15px;
          font-weight: 400;
          color: #3b4664;
          cursor: pointer;
          background: linear-gradient(180deg, #cbccce 0%, #e7ecf0 100%);
          border-right: 1px solid #fff;
          transition: background 0.2s, color 0.2s;

          &:last-child {
            border-right: none;
          }
        }

        .tab-item.active {
          color: #fff;
          background: #00918c;
        }
      }

      .question-content {
        flex: 1;
        width: 100%;
        overflow: auto;
      }
    }
  }
}
</style>
