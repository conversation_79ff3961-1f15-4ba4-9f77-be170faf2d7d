<template>
  <div class="user-record-container">
    <div class="container-header">
      <div class="left">
        <div class="filter-row">
          <el-input
            v-model="queryParams.search"
            placeholder="请输入关键字"
            clearable
            size="large"
          />
        </div>
        <div class="btn primary-btn" @click="handleQuery">
          <i-ep-search /> 搜索
        </div>
      </div>
    </div>
    <div class="content">
      <el-table
        v-loading="loading"
        :data="tableData"
        height="100%"
        border
        fit
        highlight-current-row
      >
        <el-table-column label="用户名" align="center" min-width="80">
          <template #default="scope">
            {{ scope.row.name || "绿知学员" + scope.row.id }}
          </template>
        </el-table-column>
        <el-table-column label="头像" align="center" min-width="60">
          <template #default="scope">
            <div class="cover-name">
              <img
                :src="scope.row.avatar || userStore.defaultAvatar"
                alt=""
                class="cover-img"
              />
            </div>
          </template>
        </el-table-column>
        <el-table-column label="手机号码" align="center" min-width="100">
          <template #default="scope">
            {{ scope.row.mobile }}
          </template>
        </el-table-column>
        <el-table-column label="状态" align="center" min-width="40">
          <template #default="scope">
            <el-tag :type="statusMap[scope.row.status]?.type">
              {{ statusMap[scope.row.status]?.label }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="注册时间" align="center" min-width="100">
          <template #default="scope">
            {{ scope.row.created_at }}
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center" min-width="80">
          <!-- fixed="right" -->
          <template #default="scope">
            <div class="option-btn">
              <!-- <div class="btn primary-btn" @click="onRowClick(scope.row)">
                详情
              </div> -->
              <div
                class="btn primary-btn"
                v-if="scope.row.status == 20"
                @click="onRowClick('edit', scope.row)"
                v-show="enablePerform"
              >
                解除冻结
              </div>
              <div
                class="btn info-btn"
                v-if="scope.row.status !== 20"
                @click="onRowClick('edit', scope.row)"
                v-show="enablePerform"
              >
                冻结
              </div>
              <!-- <div
                class="btn primary-btn"
                @click="handleBestowal(scope.row)"
                v-show="enableBestowal"
              >
                绿豆赠予
              </div> -->
              <!-- @click="handleCourseDonate(scope.row)"
                v-show="enableCourseDonate" -->
              <div
                class="btn give-btn"
                @click="onRowClick('donate', scope.row)"
              >
                赠予
              </div>
            </div>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <div class="footer">
      <pagination
        v-if="total > 0"
        v-model:total="total"
        v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize"
        @pagination="getData"
      />
    </div>

    <!-- 赠予弹窗 -->
    <el-dialog
      class="donate-dialog"
      v-model="donateDialog.visible"
      :title="donateDialog.title"
      :width="donateDialog.width"
      append-to-body
      @close="closeDonateDialog"
    >
      <template #header>
        <div class="dialog-header">
          <div class="header-title">赠予</div>
          <div class="desc">
            <div class="left">
              <span
                >用户名：{{
                  userDetail.name || "绿知学员" + userDetail.id
                }}</span
              >
              <span>手机号：{{ userDetail.mobile }}</span>
            </div>
            <div class="middle">
              <!-- <template v-if="donateType == 'bean'">
                <span>当前绿豆：{{ userDetail.bean }}</span>
              </template> -->
              <!-- <template v-if="donateType == 'vip'">
              </template> -->
              <span
                >当前账户：{{
                  userDetail.vip?.is_valid ? "已开通会员" : "未开通会员"
                }}</span
              >
              <div v-if="donateType == 'course'" class="course-expired">
                <span>有效期至：</span>
                <el-date-picker
                  class="date-picker"
                  v-model="courseDonateForm.expired_at"
                  type="date"
                  size="large"
                  placeholder="请选择日期"
                  clearable
                />
              </div>
            </div>
          </div>
        </div>
      </template>
      <div class="dialog-body">
        <div class="pre-header">
          <el-tabs
            v-model="donateType"
            class="header-tabs"
            @tab-change="handleTabChange"
          >
            <el-tab-pane
              v-for="(item, index) in !userDetail.vip?.is_valid
                ? donateTypeOptions
                : donateTypeOptions.filter((option) => option.value !== 'vip')"
              :key="index"
              :label="item.label"
              :name="item.value"
            />
          </el-tabs>
        </div>
        <template v-if="donateType == 'bean'">
          <el-form label-width="5rem">
            <el-form-item label="绿豆赠予">
              <el-input
                v-model="formData.beanAmount"
                placeholder="请输入需要赠予的绿豆数"
                clearable
                size="large"
              />
            </el-form-item>
            <el-form-item label="备注">
              <el-input
                v-model="formData.remark"
                placeholder="请输入备注"
                clearable
                size="large"
              />
            </el-form-item>
          </el-form>
        </template>
        <template v-if="donateType == 'vip'">
          <el-form label-width="5rem">
            <el-form-item label="截止日期">
              <el-date-picker
                class="date-picker"
                v-model="formData.expired_at"
                type="date"
                size="large"
                placeholder="请选择截止日期"
                clearable
              />
            </el-form-item>
            <el-form-item label="备注">
              <el-input
                v-model="formData.remark"
                placeholder="请输入备注"
                clearable
                size="large"
              />
            </el-form-item>
          </el-form>
        </template>
        <template v-if="donateType == 'course'">
          <div class="dialog-content-header">
            <div class="filter-row">
              <el-input
                v-model="courseQueryParams.search"
                placeholder="请输入关键字"
                clearable
                size="large"
              />
            </div>
            <div class="btn primary-btn" @click="handleCourseQuery">
              <i-ep-search /> 搜索
            </div>
            <div class="filter-row">
              <el-select
                size="large"
                v-model="courseQueryParams.cat_id"
                placeholder="请选择分类"
                filterable
                clearable
                :suffix-icon="`CaretBottom`"
                @change="handleCourseQuery"
              >
                <el-option
                  v-for="item in cat_options"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </div>
          </div>
          <div class="dialog-content-table">
            <el-table
              v-loading="courseLoading"
              element-loading-text="Loading"
              element-loading-background="#ffffffb4"
              :data="courseTableList"
              height="100%"
              border
              fit
              highlight-current-row
              ref="courseRef"
              @select="selectCourse"
              @select-all="selectCourse"
            >
              <el-table-column
                type="selection"
                align="center"
                min-width="20"
                class="dialog-checkbox2"
              />

              <el-table-column label="名称" align="center" min-width="140">
                <template #default="scope">
                  <div class="cover-name">
                    <img :src="scope.row.thumb" alt="" class="cover-img" />
                    <div class="cover-text">
                      <span>{{ scope.row.title }}</span>
                      <span
                        v-if="scope.row.has_chapter"
                        class="composite-course"
                        @click="handleCompositeCourse(scope.row)"
                      >
                        套课
                        <i-ep-caret-bottom />
                      </span>
                    </div>
                  </div>
                </template>
              </el-table-column>
              <el-table-column
                label="所属分类"
                align="center"
                min-width="80"
                show-overflow-tooltip
              >
                <template #default="scope">
                  {{ filterCategoriesName(scope.row.categories) }}
                </template>
              </el-table-column>
            </el-table>
          </div>
          <div class="dialog-content-footer">
            <pagination
              v-if="courseTotal > 0"
              v-model:total="courseTotal"
              v-model:page="courseQueryParams.pageNum"
              v-model:limit="courseQueryParams.pageSize"
              @pagination="getCourseData"
            />
          </div>
        </template>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <div class="left" v-if="donateType == 'course'">
            <span>已选{{ courseDonateForm.courses.length }}</span>
          </div>
          <div class="middle"></div>
          <div class="right">
            <div class="btn cancel-btn" @click="closeDonateDialog">取 消</div>
            <div class="btn primary-btn" @click="handleDonateSubmit">提 交</div>
          </div>
        </div>
      </template>
    </el-dialog>

    <!-- 课程赠予-套课选中弹窗 -->
    <el-dialog
      class="composite-course-dialog"
      v-model="compositeCourseDialog.visible"
      :title="compositeCourseDialog.title"
      :width="compositeCourseDialog.width"
      append-to-body
      @close="closeCompositeCourseDialog"
    >
      <template #header>
        <div class="dialog-header">
          <div class="course-desc">
            <div class="course-img">
              <img :src="compositeCourseDetail.thumb" alt="" />
            </div>
            <div class="desc">
              <div class="title">{{ compositeCourseDetail.title }}</div>
              <div class="count">
                <svg-icon class="icon-img icon-book" icon-class="book" />
                课程：{{ totalCourse(compositeCourseDetail) }}
              </div>
              <div class="time">
                <svg-icon class="icon-img icon-clock" icon-class="clock" />
                时长：{{
                  secondsToHoursAndMinutes(compositeCourseDetail.duration || 0)
                }}
              </div>
            </div>
          </div>
        </div>
      </template>
      <div class="dialog-body">
        <div class="course-menus">
          <el-scrollbar
            style="width: 100%; height: 100%"
            warp-style="overflow-x: hidden;"
          >
            <el-tree
              class="dialog-tree course-menus-tree"
              :props="compositeCourseProps"
              ref="compositeCourseTreeRef"
              :data="compositeCourseTree"
              :default-expand-all="true"
              highlight-current
              node-key="id"
              show-checkbox
              :default-checked-keys="compositeCourseFrom.lessons_id"
              @check="handleSectionCheck"
            />
          </el-scrollbar>

          <!-- @check-change="handleCheckedChange" -->
        </div>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <div class="left">
            <span>已选{{ compositeCourseFrom.lessons_id?.length || 0 }}</span>
          </div>
          <div class="right">
            <div class="btn cancel-btn" @click="closeCompositeCourseDialog">
              取 消
            </div>
            <div class="btn primary-btn" @click="handleCompositeCourseSubmit">
              确 定
            </div>
          </div>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { useUserStore } from "@/store/modules/user";
import { status } from "nprogress";
import { useRoute, useRouter } from "vue-router";
import { getUsers, updateUsers, giveGBean, giveOrder } from "@/api/user";
import { getCategories, getCourses, getCoursesDetail } from "@/api/course";
import { getSections } from "@/api/web-page";
import {
  parseTime,
  resetReactiveObject,
  secondsToHoursAndMinutes,
  numberToChinese,
  numberToChinese2,
} from "@/utils";
import { checkUserPermission } from "@/utils/auth";
const { proxy } = getCurrentInstance() as any;

// proxy 替代 this
defineOptions({
  name: "UserRecord",
  inheritAttrs: false,
});

/**路由对象*/
const route = useRoute();
/**路由实例*/
const router = useRouter();
const userStore = useUserStore();

const search = ref("");
const loading = ref(false);
// const removeIds = ref([]); // 删除用户ID集合 用于批量删除
const queryParams = reactive<any>({
  search: "",
  pageNum: 1,
  pageSize: 20,
});
// const dateTimeRange = ref("");
const total = ref(0); // 数据总数
const tableData = ref([]);
const statusMap = reactive<any>({
  10: { type: "success", label: "正常" },
  20: { type: "info", label: "冻结" },
  30: { type: "danger", label: "删除" },
  // add more status mappings as needed
});

const rowId = ref();

const donateDialog = reactive<any>({
  visible: false,
  type: "donateForm",
  width: "30%",
  title: "赠予",
});
const donateType = ref<any>("bean");
const donateTypeMap = reactive<any>({
  bean: { value: "bean", label: "绿豆" },
  course: { value: "course", label: "课程" },
  vip: { value: "vip", label: "会员" },
});
const donateTypeOptions = reactive<any>([
  { value: "bean", label: "绿豆" },
  { value: "course", label: "课程" },
  { value: "vip", label: "会员" },
]);
// 用户表单数据
const formData = reactive<any>({
  name: "",
  mobile: "",
  beanAmount: 0,
  remark: "",
});

// 冻结解冻
const enablePerform = computed(() => {
  return checkUserPermission(userStore.userInfo.permissions, 3);
});

// 绿豆赠予
const enableBestowal = computed(() => {
  return checkUserPermission(userStore.userInfo.permissions, 4);
});

// 课程赠予
const enableCourseDonate = computed(() => {
  return checkUserPermission(userStore.userInfo.permissions, 22);
});

//课程赠予相关
const userDetail = reactive<any>({}); // 用户详情数据
const courseDonateDialog = reactive<any>({
  visible: false,
  type: "courseDonateTable",
  width: "40%",
  title: "课程赠予",
});
const courseLoading = ref(false);
const courseTableList = ref<any>([]); // 课程列表数据
const courseTotal = ref(0); // 课程列表数据总数
const courseQueryParams = reactive<any>({
  search: "",
  pageNum: 1,
  pageSize: 20,
  cat_id: "",
  sec_id: "",
});
const sec_options = ref<any>([]);
const cat_options = ref<any>([]);
const courseRef = ref<any>(null);
const courseDonateForm = reactive<any>({
  expired_at: "",
  courses: [],
  receiver_id: "",
});

const compositeCourseDialog = reactive<any>({
  visible: false,
  type: "compositeCourse",
  width: "30%",
  title: "套课赠予",
});
const compositeCourseTreeRef = ref<any>(null);
const compositeCourseDetail = reactive<any>({}); // 课程详情
const compositeCourseFrom = reactive<any>({}); // 套课表单
const compositeCourseTree = ref<any>([]); // 套课数据
const compositeCourseProps = reactive<any>({
  //自定义label
  label: (data: { label: any }) => {
    return data.label; // name为你要显示的名称 可以自定义，就是将name替换label
  },
  children: "children",
});
// watch(dateTimeRange, (newVal) => {
//   if (newVal) {
//     queryParams.startTime = newVal[0];
//     queryParams.endTime = newVal[1];
//   }
// });
onMounted(() => {
  getData();
});
function getData() {
  const params = {
    search: queryParams.search || undefined,
    per_page: queryParams.pageSize,
    page: queryParams.pageNum,
  };
  getUsers(params).then((res: any) => {
    tableData.value = res.data.users.map((item: any) => {
      item.created_at = parseTime(item.created_at, "{y}-{m}-{d} {h}:{i}:{s}");
      return item;
    });
    total.value = res.total;
    loading.value = false;
  });
}

function onRowClick(type: any, row: any) {
  if (type == "edit") {
    // dialog.visible = true;
    ElMessageBox.confirm(
      `此操作将${row.status !== 20 ? "冻结" : "解冻"}该用户，是否继续?`,
      "提示",
      {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }
    ).then(() => {
      const data: any = {};
      if (row.status !== 20) {
        data.status = 20;
      } else if (row.status == 20) {
        data.status = 10;
      }
      updateUsers(row.id, data).then((res: any) => {
        getData();
        ElMessage.success({
          message: "操作成功!",
        });
      });
    });
  }
  if (type == "donate") {
    // 赠予弹窗
    donateDialog.visible = true;
    rowId.value = row.id;
    Object.assign(userDetail, row);
    // if (userDetail.vip?.is_valid) {
    //   donateTypeOptions.splice(2, 1); // 移除会员赠予选项
    // } else {
    //   donateTypeOptions.splice(2, 0, {
    //     value: "vip",
    //     label: "会员",
    //   }); // 添加会员赠予选项
    // }
  }
}
function handleQuery() {
  queryParams.pageNum = 1;
  getData();
}

function handleBeanSubmit() {
  const data = {
    user_id: rowId.value,
    bean_amount: parseInt(formData.beanAmount, 10),
    remark: formData.remark,
  };
  if (!data.bean_amount || data.bean_amount <= 0) {
    ElMessage.warning("请输入赠予的绿豆数");
    return;
  }
  giveGBean(data).then((res: any) => {
    if (res.status == 200) {
      ElMessage.success({
        message: "赠予成功!",
      });
      closeDonateDialog();
      getData();
    }
  });
}

//课程赠予相关

//课程分类、栏目显示
function filterCategoriesName(value: any) {
  let res = "--";
  res = value?.map((item: any) => item.name).join(",");
  return res;
}
function filterSectionsName(value: any) {
  let res = "--";
  res = value?.map((item: any) => item.name).join(",");
  return res;
}
function getSearchOptions() {
  const params = {
    per_page: 9999,
    page: 1,
  };
  getCategories(params).then((res: any) => {
    cat_options.value = res.data.categories.map((item: any) => {
      // item.created_at = parseTime(item.created_at, "{y}-{m}-{d} {h}:{i}:{s}");
      return {
        label: item.name,
        value: item.id,
      };
    });
  });
  getSections(params).then((res: any) => {
    sec_options.value = res.data.sections.map((item: any) => {
      // item.created_at = parseTime(item.created_at, "{y}-{m}-{d} {h}:{i}:{s}");
      return {
        label: item.name,
        value: item.id,
      };
    });
  });
}
function getCourseData() {
  courseLoading.value = true;
  const params = {
    search: courseQueryParams.search || undefined,
    page: courseQueryParams.pageNum,
    per_page: courseQueryParams.pageSize,
    cat_id: courseQueryParams.cat_id || undefined,
    sec_id: courseQueryParams.sec_id || undefined,
    status: 20,
  };
  getCourses(params)
    .then((res: any) => {
      courseTableList.value = res.data.courses.map((item: any) => {
        item.created_at = parseTime(item.created_at, "{y}-{m}-{d} {h}:{i}:{s}");
        item.published_at = parseTime(
          item.published_at,
          "{y}-{m}-{d} {h}:{i}:{s}"
        );
        return item;
      });
      courseTotal.value = res.total;
      courseLoading.value = false;
    })
    .catch((e) => {
      courseLoading.value = false;
    });
}
function handleCourseQuery() {
  courseQueryParams.pageNum = 1;
  getCourseData();
}

function totalCourse(val) {
  let res = 0;
  let chapter = 0;
  let video = 0;
  const courseChapters = val.chapters;
  const videoList = val.videos;
  if (courseChapters?.length > 0) {
    courseChapters.forEach((item) => {
      chapter++;
      if (item.videos.length) {
        item.videos.forEach((itm) => {
          // res++;
          video++;
        });
      }
    });
  } else if (videoList?.length > 0) {
    videoList.forEach((item) => {
      // res++;
      video++;
    });
  }
  return chapter != 0 ? chapter + "章" + video + "节" : video + "课时";
}
// 课程多选
function selectCourse(selection: any) {
  const originalSelect =
    JSON.parse(JSON.stringify(courseDonateForm.courses)) ||
    courseDonateForm.courses;
  const selectMap = selection.map((item: any) => {
    const res = {
      id: item.id,
      // lesson_ids: item.lesson_ids,
    };
    return item.id;
  });

  // courseRef
  courseDonateForm.courses = [];
  selectMap.forEach((item: any) => {
    const index = originalSelect.findIndex((it: any) => it.id == item);
    if (index >= 0) {
      courseDonateForm.courses.push(originalSelect[index]);
    } else {
      courseDonateForm.courses.push({
        id: item,
      });
    }
  });
  console.log("courseDonateForm", courseDonateForm);
}

function closeCourseDonateDialog() {
  courseDonateDialog.visible = false;
  resetReactiveObject(courseDonateForm);
  resetReactiveObject(userDetail);
}
async function handleCourseDonateSubmit() {
  let course_content: any = []; // [{ course_id: "", lesson_ids: "" }];
  for (const item of courseDonateForm.courses) {
    const courseData: any = {
      course_id: item.id,
      lesson_ids: "all",
    };
    if (item.lesson_ids && item.lesson_ids.length > 0) {
      courseData.lesson_ids = item.lesson_ids?.join(",");
    }

    course_content.push(courseData);
  }

  if (!courseDonateForm.expired_at) {
    ElMessage.warning("请输入赠予截至日期");
    return;
  }
  const data = {
    receiver_id: courseDonateForm.receiver_id,
    content: JSON.stringify(course_content),
    expired_at: parseTime(courseDonateForm.expired_at, "{y}{m}{d}235959"),
  };
  // console.log("data", data, course_content, courseDonateForm.courses);
  try {
    const res = await giveOrder(data);
    if (res.status === 200) {
      ElMessage.success({
        message: "赠予成功!",
      });
      closeDonateDialog();
      getData();
    }
  } catch (error: any) {
    ElMessage.error(error);
    console.error("提交失败", error);
  }
}

// 课程赠予-套课
function handleCompositeCourse(row: any) {
  compositeCourseDetail.id = row.id;
  compositeCourseDialog.visible = true;
  getCoursesDetail(row.id).then((res: any) => {
    Object.assign(compositeCourseDetail, res.data);
    compositeCourseDetail.title = row.title;
    if (res.data.chapters && res.data.chapters.length > 0) {
      compositeCourseTree.value = res.data.chapters.map((item: any) => {
        let res = {
          id: item.id + item.title + "",
          label: item.title,
          type: "chapter",
          children: item.videos.map((itm: any) => {
            return {
              id: itm.id,
              label: itm.name,
            };
          }),
        };
        return res;
      });
    }

    // 确保树数据加载完成后设置选中项
    nextTick(() => {
      const alreadySelect = courseDonateForm.courses.filter(
        (item: any) => item.id === row.id
      );

      if (alreadySelect.length > 0) {
        alreadySelect[0].lesson_ids.forEach((item: any) => {
          const node = proxy?.$refs.compositeCourseTreeRef.getNode(item);
          if (node) {
            proxy?.$refs.compositeCourseTreeRef.setChecked(node, true);
          } else {
            console.error(` ${item}`);
          }
        });
      }
    });
  });
}
function handleSectionCheck(data, checkedNodes) {
  const alreadyCheck: any = compositeCourseTreeRef.value.getCheckedNodes();
  console.log("alreadyCheck--taoke", alreadyCheck);
  compositeCourseFrom.lessons_id = alreadyCheck
    .filter((item: any) => {
      return !item.type;
    })
    .map((item: any) => item.id);

  // 另外定义，避免覆盖
  // lessons_ids.value = checkedNodes.checkedKeys.concat(
  //   checkedNodes.halfCheckedKeys
  // );//halfCheckedKeys--当前半选中的节点，应该用不上？
}
function handleCheckedChange(data: any, checked: any, indeterminate: any) {
  // console.log("status", checked);
}
function closeCompositeCourseDialog() {
  compositeCourseDialog.visible = false;
  compositeCourseTree.value = [];
  resetReactiveObject(compositeCourseDetail);
  resetReactiveObject(compositeCourseFrom);
}
// 课程赠予-套课提交
function handleCompositeCourseSubmit() {
  if (compositeCourseFrom.lessons_id.length > 0) {
    const selectRow: any = courseTableList.value.filter(
      (item: any, index: any) => {
        return compositeCourseDetail.id == item.id;
      }
    );
    courseRef.value.toggleRowSelection(selectRow[0], true);
    const flag = courseDonateForm.courses.some(
      (item: any) => item.id == compositeCourseDetail.id
    );
    if (!flag) {
      courseDonateForm.courses.push({
        id: compositeCourseDetail.id,
        lesson_ids: compositeCourseFrom.lessons_id,
      });
    } else {
      courseDonateForm.courses.forEach((item: any) => {
        if (item.id == compositeCourseDetail.id) {
          item.lesson_ids = compositeCourseFrom.lessons_id;
        }
      });
    }
  }
  setTimeout(() => {
    closeCompositeCourseDialog();
    console.log("courseDonateForm.courses", courseDonateForm.courses);
  }, 200);
}

async function handleVipDonateSubmit() {
  if (userDetail.vip?.is_valid) {
    ElMessage.warning("该用户已购买会员，不能赠予");
    return;
  }
  if (!formData.expired_at) {
    ElMessage.warning("请输入赠予会员截至日期");
    return;
  }
  const data = {
    receiver_id: rowId.value,
    type: 30,
    content: JSON.stringify(""),
    // content: JSON.stringify([
    //   {
    //     from: parseTime(new Date(), "{y}{m}{d}{h}{i}{s}"),
    //     till: parseTime(formData.expired_at, "{y}{m}{d}235959"),
    //   },
    // ]),
    expired_at: parseTime(formData.expired_at, "{y}{m}{d}235959"),
  };

  try {
    const res = await giveOrder(data);
    if (res.status === 200) {
      ElMessage.success({
        message: "赠予会员成功!",
      });
      closeDonateDialog();
      getData();
    }
  } catch (error: any) {
    ElMessage.error(error);
    console.error("提交失败", error);
  }
}

function handleTabChange(tab: any) {
  donateDialog.width = "30%";
  if (donateType.value == "bean") {
    resetReactiveObject(formData);
  }
  if (donateType.value == "course") {
    courseDonateForm.receiver_id = rowId.value;
    donateDialog.width = "40%";
    getSearchOptions();
    getCourseData();
  }
  if (donateType.value == "vip") {
    // formData.receiver_id = rowId.value;
    resetReactiveObject(formData);
  }
}
function closeDonateDialog() {
  if (donateType.value == "bean") {
    resetReactiveObject(formData);
  }
  if (donateType.value == "course") {
    resetReactiveObject(courseDonateForm);
  }
  if (donateType.value == "vip") {
    resetReactiveObject(formData);
  }
  resetReactiveObject(userDetail);
  donateDialog.visible = false;
  donateType.value = "bean";
}
function handleDonateSubmit() {
  if (donateType.value == "bean") {
    handleBeanSubmit();
  }
  if (donateType.value == "course") {
    handleCourseDonateSubmit();
  }
  if (donateType.value == "vip") {
    handleVipDonateSubmit();
  }
}
</script>

<style scoped lang="scss">
.user-record-container {
  // width: 50%;
  height: 95%;
  margin: 20px;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 3px 20px 1px rgb(109 125 126 / 7%);

  .container-header {
    display: flex;
    justify-content: space-between;
    padding: 20px;

    .left {
      display: flex;
      justify-content: space-between;
      // width: 40%;
      .btn {
        width: 116px;
        height: 40px;
        margin-left: 20px;
      }
    }

    .filter-row {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 260px;
      margin-left: 20px;

      &:nth-child(1) {
        width: 260px;
        margin-left: 0;
      }

      .btn {
        width: 116px;
        height: 40px;
        margin-left: 20px;
      }
    }
  }

  .content {
    width: 100%;
    height: calc(100% - 170px);
    padding: 10px 20px;

    .cover-name {
      display: flex;
      align-items: center;

      .cover-img {
        width: 50px;
        height: 50px;
        border-radius: 50%;
      }

      span {
        display: inline-block;
        width: 40%;
      }
    }
  }

  .footer {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-top: 15px;
  }
}
</style>

<style lang="scss">
.donate-dialog {
  padding: 0 !important;

  .el-dialog__header,
  .el-dialog__header.show-close {
    padding: 0 !important;
    margin-bottom: 0 !important;
  }

  .el-dialog__body {
    padding: 0 1rem !important;
  }

  .el-dialog__footer {
    padding: 0 !important;
  }

  .dialog-header {
    display: flex;
    flex-direction: column;
    width: 100%;
    height: 174px;
    color: #fff !important;
    background: url("@/assets/images/header-bg.png") no-repeat;
    background-size: 100% 100%;

    .header-title {
      padding: 10px 0;
      font-size: 36px;
      font-weight: 500;
    }

    .desc {
      display: flex;
      align-items: center;
      justify-content: space-between;
      width: 100%;
      padding: 10px 60px;
      font-size: 18px;
      font-weight: 500;

      span {
        padding: 5px 0;
      }

      .left {
        display: flex;
        flex-direction: column;
        text-align: left;
      }

      .middle {
        display: flex;
        flex-direction: column;
        text-align: left;

        .course-expired {
          display: flex;
          align-items: center;
          justify-content: center;
        }

        .el-input__wrapper {
          background-color: #fff !important;
          border: 1px solid #7b8d9c70 !important;
          box-shadow: inset 1px 1px 6px 1px #7b8d9c39 !important;
        }
      }
    }
  }

  .dialog-body {
    .dialog-content-header {
      display: flex;
      justify-content: space-between;
      padding: 20px;

      .left {
        display: flex;
        justify-content: space-between;
        // width: 40%;
      }

      .btn {
        width: 116px;
        height: 40px;
        margin-left: 20px;
      }

      .filter-row {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 260px;
        margin-left: 20px;

        &:nth-child(1) {
          width: 260px;
          margin-left: 0;
        }

        .btn {
          width: 116px;
          height: 40px;
          margin-left: 20px;
        }
      }
    }

    .dialog-content-table {
      width: 100%;
      height: 40vh;
      padding: 10px 20px;

      .cover-name {
        display: flex;
        align-items: center;

        .cover-img {
          // width: 150px !important;
          // height: 100px !important;
          width: 162px !important;
          height: 86px !important;
          object-fit: cover;
          border-radius: 8px;
        }

        .cover-text {
          display: flex;
          flex-direction: column;
          align-items: center;
          width: 40%;
        }
      }

      .composite-course {
        margin-top: 5px;
        font-size: 15px;
        color: #00918c;
        cursor: pointer;
      }
    }

    .dialog-content-footer {
      display: flex;
      align-items: center;
      justify-content: center;
      margin-top: 15px;
    }
  }

  .pre-header {
    padding: 10px 20px !important;

    .el-tabs__item {
      font-size: 18px !important;
    }
  }

  .dialog-footer {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 10px 20px 30px;
    border-radius: 0 0 0.9375rem 0.9375rem;

    .left {
      display: flex;
      align-items: center;

      span {
        padding: 2px 0;
        font-size: 20px;
        font-weight: 500;
        color: #00918c;
      }
    }

    .middle {
      display: flex;
      align-items: center;
      justify-content: center;

      .el-input__wrapper {
        background-color: #fff !important;
        border: 1px solid #7b8d9c70 !important;
        box-shadow: inset 1px 1px 6px 1px #7b8d9c39 !important;
      }
    }

    .right {
      display: flex;
      align-items: center;
    }
  }
}

.composite-course-dialog {
  padding: 0 !important;

  .el-dialog__header,
  .el-dialog__header.show-close {
    padding: 0 !important;
    margin-bottom: 0 !important;
  }

  .el-dialog__body {
    padding: 0 1rem !important;
  }

  .dialog-header {
    display: flex;
    align-items: center;
    width: 100%;
    height: 216px;
    padding-left: 50px;
    background: linear-gradient(180deg, #eaf7ec 0%, #fff 100%);
    background-size: 100% 100%;
    border-radius: 8px 8px 0 0;

    .course-desc {
      display: flex;
      align-items: center;

      .course-img {
        width: 243px;
        height: 129px;
        overflow: hidden;
        border-radius: 16px;

        img {
          width: 100%;
          height: 100%;
          object-fit: cover;
        }
      }

      .desc {
        margin-left: 20px;
        text-align: left;

        .title {
          margin-bottom: 30px;
          font-size: 23px;
          font-weight: 500;
          color: #3b4664;
        }

        .count,
        .time {
          font-size: 18px;
          font-weight: 400;
          color: #3d4040;
        }
      }
    }
  }

  .dialog-body {
    display: flex;
    align-items: center;
    justify-content: center;

    .course-menus {
      display: flex;
      width: 90%;
      height: 40vh;
      border: 1px solid #edeff4;
      border-radius: 8px;
      box-shadow: inset 3px 3px 6px 1px rgb(188 198 214 / 14%);
    }

    .course-menus-tree {
      width: 100%;
    }
  }

  .dialog-footer {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 10px 20px 30px;

    .left {
      display: flex;
      text-align: left;

      span {
        padding: 2px 0;
        font-size: 20px;
        font-weight: 500;
        color: #00918c;
      }
    }

    .right {
      display: flex;
      align-items: center;
    }
  }
}
</style>
