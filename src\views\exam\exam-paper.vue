<template>
  <div class="exam-paper-container">
    <div class="container-header">
      <div class="left">
        <div class="filter-row">
          <el-input
            v-model="queryParams.search"
            placeholder="请输入试卷名称"
            clearable
            size="large"
          />
        </div>

        <div class="btn primary-btn" @click="handleQuery">
          <i-ep-search /> 搜索
        </div>
        <div class="filter-row">
          <el-select
            :suffix-icon="`CaretBottom`"
            size="large"
            v-model="queryParams.q_bank_id"
            placeholder="请选择题库"
            filterable
            clearable
            @change="handleQuery"
          >
            <el-option
              v-for="item in qbDataOptions"
              :key="item.id"
              :label="item.name"
              :value="item.id"
            />
          </el-select>
        </div>
      </div>
      <div class="right">
        <div class="text-btn" @click="handleCreate">
          <i-ep-circle-plus style="margin-right: 5px" />
          新增试卷
        </div>
      </div>
    </div>

    <div class="content">
      <el-table
        v-loading="loading"
        element-loading-text="Loading"
        element-loading-background="#ffffffb4"
        :data="tableData"
        height="100%"
        border
        fit
        highlight-current-row
      >
        <el-table-column label="试卷名称" align="center" min-width="140">
          <template #default="scope">
            <div class="paper-name">
              <span>{{ scope.row.name }}</span>
            </div>
          </template>
        </el-table-column>

        <el-table-column label="题目数量" align="center" min-width="80">
          <template #default="scope">
            <span style="color: #00918c"
              >{{ scope.row?.gk_questions?.length || "--" }}
            </span>
          </template>
        </el-table-column>

        <el-table-column label="总分" align="center" min-width="60">
          <template #default="scope">
            <span style="font-weight: 500; color: #1c8d84"
              >{{ scope.row.total_scores }}分</span
            >
          </template>
        </el-table-column>

        <el-table-column label="创建时间" align="center" min-width="120">
          <template #default="scope">
            {{ scope.row.created_at }}
          </template>
        </el-table-column>

        <el-table-column label="状态" align="center" min-width="80">
          <template #default="scope">
            <el-tag :type="scope.row.enabled === true ? 'success' : 'danger'">
              {{ scope.row.enabled === true ? "启用" : "停用" }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="创建人" align="center" min-width="60">
          <template #default="scope">
            {{ scope.row.creator || "--" }}
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center" min-width="180">
          <template #default="scope">
            <div class="option-btn">
              <div
                class="btn light-blue-btn"
                @click="onRowClick('preview', scope.row)"
              >
                预览
              </div>
              <div
                class="btn"
                :class="[
                  scope.row.enabled === false ? 'light-green-btn' : 'info-btn',
                ]"
                @click="onRowClick('status', scope.row)"
              >
                {{ scope.row.enabled === false ? "启用" : "停用" }}
              </div>

              <div
                class="btn primary-btn"
                @click="onRowClick('edit', scope.row)"
              >
                编辑
              </div>
              <div
                class="btn delete-btn"
                @click="onRowClick('delete', scope.row)"
              >
                删除
              </div>
            </div>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <div class="footer">
      <pagination
        v-if="total > 0"
        v-model:total="total"
        v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize"
        @pagination="getData"
      />
    </div>

    <!-- 试卷预览弹窗 -->

    <el-dialog
      class="preview-dialog"
      v-model="previewDialog.visible"
      :title="previewDialog.title"
      :width="previewDialog.width"
      append-to-body
      @close="closePreviewDialog"
    >
      <template #header>
        <div class="dialog-title">
          {{ previewDialog.title }}
        </div>
      </template>
      <div class="dialog-body">
        <el-scrollbar
          style="width: 100%; height: 100%"
          warp-style="overflow-x: hidden;"
        >
          <div class="paper-preview-header">
            <div class="title">{{ previewPaperInfo.name || "单元测试" }}</div>
            <div class="remark">{{ previewPaperInfo.remark }}</div>
            <div class="description">
              <span
                >共 {{ previewPaperInfo.question_count }} 题 | 总分
                {{ previewPaperInfo.total_scores }} 分 | 限时
                {{ previewPaperInfo.time_limit }} 分钟</span
              >
            </div>
          </div>
          <div class="paper-preview-container">
            <ExamPaper
              :isOperable="false"
              :showAnalysis="false"
              :showAnswerType="'all'"
              :showSelectedAnswer="false"
              :showQuestionScore="true"
              :itemStyle="'shadowBorder'"
              :showAllAnswer="true"
              :show-get-score="false"
              :show-answer-time="false"
              :paperData="previewPaperInfo.questions"
            />
          </div>
        </el-scrollbar>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <div class="btn primary-btn" @click="closePreviewDialog">确 定</div>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { useUserStore } from "@/store/modules/user";
import { status } from "nprogress";
import { useRoute, useRouter } from "vue-router";
import ExamPaper from "./components/ExamPaper.vue";

import {
  parseTime,
  resetReactiveObject,
  secondsToHoursAndMinutes,
  numberToChinese,
  numberToChinese2,
} from "@/utils";
import { checkUserPermission } from "@/utils/auth";
import {
  getQuestionBanks,
  getExamPapers,
  getExamPaperDetail,
  updateExamPaper,
  deleteExamPaper,
} from "@/api/exam";

defineOptions({
  name: "ExamPaper",
  inheritAttrs: false,
});

const route = useRoute();
const router = useRouter();
const loading = ref(false);
const total = ref(0);

const queryParams = reactive({
  search: "",
  q_bank_id: "",
  pageNum: 1,
  pageSize: 20,
});
const qbDataOptions = ref<any>([]);

// 试卷数据
const tableData = ref<any>([]);

// 预览弹窗
const previewDialog = reactive<any>({
  visible: false,
  type: "preview",
  width: "45%",
  title: "试卷预览",
});

const previewPaperInfo = reactive<any>({
  name: "",
  question_count: 0,
  total_scores: 0,
  questions: [],
});

onBeforeMount(() => {
  getData();
  getQbBankData();
});

onMounted(() => {});

function getQbBankData() {
  const params = {
    page: 1,
    per_page: 999,
  };

  getQuestionBanks(params).then((res: any) => {
    if (res.status === 200) {
      qbDataOptions.value = res.data.q_banks.map((item: any) => {
        return item;
      });
    }
  });
}
function getData() {
  loading.value = true;

  const params = {
    search: queryParams.search || undefined,
    page: queryParams.pageNum,
    per_page: queryParams.pageSize,
    q_bank_id: queryParams.q_bank_id || undefined,
  };

  getExamPapers(params).then((res: any) => {
    if (res.status === 200) {
      tableData.value = res.data.exam_papers.map((item: any) => {
        item.created_at = parseTime(item.created_at, "{y}-{m}-{d} {h}:{i}:{s}");
        return item;
      });
      total.value = res.total;
      loading.value = false;
    }
  });
}
// 格式化日期
// function formatDate(dateString: string) {
//   if (!dateString) return "-";
//   const date = new Date(dateString);
//   return date.toLocaleDateString("zh-CN", {
//     year: "numeric",
//     month: "2-digit",
//     day: "2-digit",
//     hour: "2-digit",
//     minute: "2-digit",
//   });
// }

function handleQuery() {
  queryParams.pageNum = 1;
  getData();
}

function handleCreate() {
  router.push({ path: "exam-paper-action", query: { type: "create" } });
}

function onRowClick(type: string, row: any) {
  switch (type) {
    case "preview":
      handlePreview(row);
      break;
    case "status":
      handleStatusChange(row);
      break;
    case "detail":
      router.push({
        path: "exam-paper-action",
        query: { id: row.id, type: "detail" },
      });
      break;
    case "edit":
      router.push({
        path: "exam-paper-action",
        query: { id: row.id, type: "edit" },
      });
      break;
    case "delete":
      handleDelete(row);
      break;
  }
}

// 预览试卷
function handlePreview(row: any) {
  getExamPaperDetail(row.id).then((res: any) => {
    previewPaperInfo.name = res.data.exam_paper.name;
    previewPaperInfo.question_count = res.data.exam_paper.questions.length;
    previewPaperInfo.total_scores = res.data.exam_paper.total_scores;
    previewPaperInfo.time_limit = res.data.exam_paper.time_limit;
    previewPaperInfo.remark = res.data.exam_paper.remark;
    previewPaperInfo.questions = res.data.exam_paper.questions.map(
      (item: any) => {
        return {
          id: item.id,
          name: item.name,
          title: item.name,
          image_urls: item.image_urls || item.images,
          remark: item.remark,
          scores: item?.scores,
          q_type: item.q_type || item.category,
          content: item.content,
          answers: item.answers,
          correctAnswer: item.answers,
          seq: item.seq,
        };
      }
    );
  });

  previewDialog.visible = true;
}

function closePreviewDialog() {
  previewDialog.visible = false;
  resetReactiveObject(previewPaperInfo);
}

function handleStatusChange(row: any) {
  const newStatus = row.enabled === false ? "启用成功" : "停用成功";
  const statusText = row.enabled === true ? "停用" : "启用";

  ElMessageBox.confirm(`确定要${statusText}试卷"${row.name}"吗？`, "状态切换", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  }).then(() => {
    updateExamPaper(row.id, { enabled: !row.enabled }).then(() => {
      ElMessage.success(newStatus);
      getData();
    });
  });
}

function handleDelete(row: any) {
  ElMessageBox.confirm(`确定要删除试卷"${row.name}"吗？`, "删除确认", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  }).then(() => {
    deleteExamPaper(row.id).then((res: any) => {
      if (res.status == 200) {
        ElMessage.success("删除成功");
        getData();
      }
    });
  });
}
</script>

<style scoped lang="scss">
.exam-paper-container {
  position: relative;
  height: 95%;
  margin: 20px;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 3px 20px 1px rgb(109 125 126 / 7%);

  .pre-header {
    width: 100%;
    padding: 20px 20px 0;
  }

  .container-header {
    display: flex;
    justify-content: space-between;
    width: 100%;
    padding: 20px;

    .left {
      display: flex;
      align-items: center;
      justify-content: space-between;
      // width: 60%;
      .btn {
        width: 116px;
        height: 40px;
        margin-left: 20px;
        font-size: 17px;
      }
    }

    .right {
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .filter-row {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 260px;
      margin-left: 20px;

      &:nth-child(1) {
        width: 260px;
        margin-left: 0;
      }

      .btn {
        width: 116px;
        height: 40px;
        margin-left: 20px;
      }
    }
  }

  .content {
    width: 100%;
    height: calc(100% - 170px);
    padding: 10px 20px;

    .course-tags {
      display: flex;
      flex-wrap: wrap;
      gap: 5px;
    }
  }

  .footer {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-top: 15px;
  }
}
</style>
<style lang="scss">
.preview-dialog {
  padding: 0 !important;

  .el-dialog__header {
    height: 88px;
    text-align: center;
    background: url("@/assets/images/dialog-header-green2.png") no-repeat;
    background-size: 100% 100%;

    .dialog-title {
      display: flex;
      align-items: center;
      justify-content: center;
      height: 100%;
      font-size: 24px;
      font-weight: 500;
      color: #fff;
    }
  }

  .el-dialog__body {
    padding: 20px !important;
  }

  .el-dialog__footer {
    padding: 20px !important;
  }

  .el-dialog__close {
    width: 21px;
    height: 21px;
    background: #fff;
    border-radius: 50%;

    svg {
      font-size: 20px;

      path {
        fill: #00918c !important;
      }
    }
  }

  .dialog-body {
    height: 65vh;
  }

  .paper-preview-header {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    margin-bottom: 10px;

    .title {
      font-size: 24px;
      font-weight: 500;
      color: #3b4664;
    }

    .remark {
      margin-top: 10px;
      font-size: 16px;
      font-weight: 400;
    }

    .description {
      margin-top: 10px;
      font-size: 18px;
      font-weight: 400;
      color: #3b4664;
    }
  }

  .paper-preview-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    width: 100%;
  }
}
</style>
