<template>
  <!-- 填空题组件 -->
  <div
    class="fill-blank-container"
    ref="fillBlankContainer"
    :style="{
      ...getContainerStyle(),
    }"
  >
    <div class="question-panel">
      <div class="question-title">
        <div
          v-if="showCheckBox"
          class="left-controls custom-checkbox-rectangle checkbox-rectangle-green"
        >
          <el-checkbox v-model="selectedQuestion" @change="handleSelect" />
        </div>

        <span class="text"
          >{{ questionIndex + 1 }}.{{ questionData.title }}
          <span class="score-tip">
            （填空题
            <span v-if="showQuestionScore">·{{ questionData.score }}分</span>
            ）
          </span>
        </span>
      </div>

      <!--  题目图片  -->
      <div
        class="title-img"
        v-if="questionData?.images && questionData?.images.length > 0"
      >
        <div
          class="img-content"
          v-for="(img, index) in questionData?.images"
          :key="index"
        >
          <!-- <img :src="img" alt="题目图片" :key="index" /> -->
          <el-image
            class="el-img"
            :src="img"
            :zoom-rate="1.1"
            :max-scale="7"
            :min-scale="0.2"
            :preview-src-list="[img]"
            :preview-teleported="true"
            :close-on-press-escape="true"
            show-progress
            fit="cover"
          />
        </div>
      </div>

      <div
        class="answer-row"
        :style="{
          'pointer-events': isOperable ? 'auto' : 'none',
        }"
      >
        <el-input
          :model-value="getInputValue()"
          :readonly="!isOperable || showAllAnswer"
          placeholder="请输入答案"
          size="large"
          clearable
          @input="updateAnswer"
        />
        <!-- placeholder="请输入答案，多个答案用'/'分隔" -->
      </div>
      <div class="result-row" v-if="showAllAnswer">
        <div
          class="result-answer"
          :class="{
            correct: isCorrect && showGetScore,
            wrong: !isCorrect && showGetScore,
          }"
        >
          <span class="highlight"
            >答案： {{ formatAnswersDisplay(questionData.answers) }} </span
          >；
          <span class="score" v-if="showGetScore"> 得分{{ score }}</span>
        </div>
        <div class="result-analysis" v-if="showAnalysis && questionData.remark">
          <div class="analysis-title">解析：{{ questionData.remark }}</div>
        </div>
        <div class="edit-operator">
          <div class="drag-sort" v-if="showDrag">
            <svg-icon icon-class="drag3" />
          </div>
          <div class="delete-icon" @click="handleDelete" v-if="showDelete">
            <svg-icon icon-class="delete" />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useAppStore } from "@/store/modules/app";
import { useRoute, useRouter } from "vue-router";
//const { proxy } = getCurrentInstance();
// proxy 替代 this
defineOptions({
  name: "FillBlank",
  inheritAttrs: false,
});
const props = defineProps({
  isOperable: {
    type: Boolean,
    default: false,
  }, //是否只读或答题
  showQuestionScore: {
    type: Boolean,
    default: false,
  }, //是否显示题目分数
  showAllAnswer: {
    type: Boolean,
    default: false,
  }, //是否显示答案
  showSelectedAnswer: {
    type: Boolean,
    default: false,
  }, //是否显示选中答案
  showGetScore: {
    type: Boolean,
    default: false,
  }, //是否显示得分
  showAnswerTime: {
    type: Boolean,
    default: false,
  }, //是否显示答题时间
  showCheckBox: {
    type: Boolean,
    default: false,
  }, //是否显示题目选中的选择框
  showDrag: {
    type: Boolean,
    default: false,
  }, //是否显示拖拽
  showDelete: {
    type: Boolean,
    default: false,
  }, //是否显示删除
  showAnalysis: {
    type: Boolean,
    default: false,
  }, //是否显示解析
  showAnswerType: {
    type: String,
    default: "all", // all-全部, correct-正确, incorrect-错误
  }, //筛选题目答完后答题类型
  itemStyle: {
    type: String,
    default: "bottomBorder", // shadowBorder-阴影边框, bottomBorder-底部边框，其它...
  }, //试题样式
  paperData: {
    type: Object,
    default: () => {},
  }, //试卷数据
  questionData: {
    type: Object,
    default: () => {
      return {};
    },
  }, //试题数据
  questionIndex: {
    type: Number,
    default: 0,
  },
  selectedQuestionIds: {
    type: Array,
    default: () => [],
  }, //外部传入的选中题目ID列表
});
const emit = defineEmits(["updateAnswer", "handleAction"]);
const store = useAppStore();
const route = useRoute();
const router = useRouter();

const questionData = computed(() => props.questionData);
const questionIndex = computed(() => props.questionIndex);
const isOperable = computed(() => props.isOperable);
const showAllAnswer = computed(() => props.showAllAnswer);
const showSelectedAnswer = computed(() => props.showSelectedAnswer);
const showQuestionScore = computed(() => props.showQuestionScore);
const showGetScore = computed(() => props.showGetScore);
const showAnswerTime = computed(() => props.showAnswerTime);
const showDrag = computed(() => props.showDrag);
const showDelete = computed(() => props.showDelete);
const showAnalysis = computed(() => props.showAnalysis);
const showAnswerType = computed(() => props.showAnswerType);
const itemStyle = computed(() => props.itemStyle);
const showCheckBox = computed(() => props.showCheckBox);

const isCorrect = computed(() => {
  // return questionData.value.userAnswer === questionData.value.answers;
  return questionData.value.is_correct;
}); //判断是否正确
const score = computed(() => {
  // return isCorrect.value ? questionData.value.score : 0;
  return questionData.value.user_scores || 0;
}); //判断得分

const answersData = ref("");
const selectedQuestion = ref<any>(false);

// 监听外部选中状态变化
watch(
  () => props.selectedQuestionIds,
  (newIds) => {
    const isSelected = newIds.includes(questionData.value.id);
    selectedQuestion.value = isSelected;
  },
  { deep: true, immediate: true }
);
onBeforeMount(() => {
  // 初始化答案数据
  initializeAnswer();
});

onMounted(() => {});

// 初始化答案
function initializeAnswer() {
  if (showSelectedAnswer.value && questionData.value?.answers) {
    answersData.value = questionData.value.answers;
  }
}

//  填空题答题处理
function updateAnswer(value: string) {
  if (!isOperable.value) return;

  answersData.value = value.trim();

  //  答题数据提交
  const answerData = {
    questionId: questionData.value.id,
    answer: answersData.value,
    questionType: questionData.value.q_type,
    questionIndex: questionIndex.value,
    timestamp: Date.now(),
  };

  emit("updateAnswer", answerData);
}
function getContainerStyle() {
  let style = {};
  if (itemStyle.value == "bottomBorder") {
    style = {
      " border-bottom": "1px solid hsl(200deg 6% 42% / 44%)",
    };
  }
  if (itemStyle.value == "shadowBorder") {
    style = {
      "box-shadow":
        "0px 0px 6px 1px rgba(191,226,206,0.08), inset 0px 0px 10px 1px rgba(19,69,65,0.22)",
      "border-radius": "6px 6px 6px 6px",
      border: "1px solid #FFFFFF",
      "margin-bottom": "0.8rem",
    };
  }
  return style;
}

function handleSelect() {
  // selectedQuestion.value = !selectedQuestion.value;
  emit("handleAction", {
    type: selectedQuestion.value ? "select-question" : "remove-question",
    question: questionData.value,
  });
}
function handleDelete() {
  emit("handleAction", {
    type: "delete-question",
    question: questionData.value,
  });
}

// 获取输入框显示值
function getInputValue() {
  if (showSelectedAnswer.value) {
    return questionData.value?.userAnswer;
  }
  return answersData.value;
}

// 格式化答案显示
function formatAnswersDisplay(answers: any) {
  if (!answers) return "";
  if (typeof answers === "string") {
    // 如果是字符串，直接返回
    return answers;
  }
  if (Array.isArray(answers)) {
    // 如果是数组，用'/'连接
    return answers.join("/");
  }
  return String(answers);
}
</script>

<style scoped lang="scss">
.fill-blank-container {
  position: relative;
  width: 100%;
  padding: 18px 18px 24px;
  border-bottom: 1px solid hsl(200deg 6% 42% / 44%);

  .question-panel {
    .el-img {
      width: 212px;
      height: 112px;
      cursor: pointer;
      object-fit: cover;
    }

    .question-title {
      display: flex;
      align-items: center;
      margin-bottom: 10px;
      font-size: 18px;
      font-weight: 500;
      color: #3b4664;

      .left-controls {
        margin-right: 10px;
      }

      .score-tip {
        font-size: 15px;
        font-weight: 500;
        color: #00918c;
      }
    }

    .title-img {
      display: flex;
      flex-wrap: wrap;
      gap: 10px;
      margin-top: 10px;

      .img-content {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 222px;
        height: 122px;
        background: #fff;
        border: 1px solid #fff;
        box-shadow: 0 0 10px 1px rgb(140 177 156 / 42%);

        img {
          width: 212px;
          height: 112px;
          object-fit: cover;
        }
      }
    }

    .answer-row {
      margin: 12px 0 0;
      font-size: 15px;
      color: #666;

      .label {
        color: #888;
      }

      .answer {
        font-weight: 500;
        color: #2ab7b0;
      }
    }

    .result-row {
      .result-answer {
        margin: 12px 0;
        font-size: 20px;
        font-weight: 500;
        color: #00918c;
      }

      .correct {
        color: #00918c;
      }

      .wrong {
        color: #f06e15;
      }
    }

    .result-analysis {
      font-size: 20px;
      font-weight: 500;
      color: #00918c;
    }

    .edit-operator {
      position: absolute;
      right: 20px;
      bottom: 20px;
      display: flex;
      justify-content: flex-end;

      svg {
        margin-left: 12px;
        font-size: 24px;
        cursor: pointer;
      }
    }
  }
}
</style>
