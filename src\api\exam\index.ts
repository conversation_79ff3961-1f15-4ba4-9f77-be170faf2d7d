import request from "@/utils/request";

//主题管理

/**
 * 获取主题列表
 * @param queryParams 查询参数
 */
export function getTopics(queryParams?: any) {
  return request({
    url: "/gkadmin/v1/qb_topics",
    method: "get",
    params: queryParams,
  });
}

/**
 * 获取主题详情
 * @param id 主题ID
 */
export function getTopicDetail(id: any) {
  return request({
    url: `/gkadmin/v1/qb_topics/${id}`,
    method: "get",
  });
}

/**
 * 新增主题
 * @param data 主题数据
 */
export function addTopic(data: any) {
  return request({
    url: "/gkadmin/v1/qb_topics",
    method: "post",
    data: data,
  });
}

/**
 * 修改主题
 * @param id 主题ID
 * @param data 主题数据
 */
export function updateTopic(id: any, data: any) {
  return request({
    url: `/gkadmin/v1/qb_topics/${id}`,
    method: "put",
    data: data,
  });
}

/**
 * 删除主题
 * @param id 主题ID
 */
export function deleteTopic(id: any) {
  return request({
    url: `/gkadmin/v1/qb_topics/${id}`,
    method: "delete",
  });
}

// 题库管理
/**
 * 获取题库列表
 * @param queryParams 查询参数
 */
export function getQuestionBanks(queryParams?: any) {
  return request({
    url: "/gkadmin/v1/q_banks",
    method: "get",
    params: queryParams,
  });
}

/**
 * 获取题库详情
 * @param id 题库ID
 */
export function getQuestionBankDetail(id: any) {
  return request({
    url: `/gkadmin/v1/q_banks/${id}`,
    method: "get",
  });
}

/**
 * 查询绑定题库的课程
 */

export function getQuestionBankCourses(queryParams?: any) {
  return request({
    url: `/gkadmin/v2/courses`,
    method: "get",
    params: queryParams,
  });
}

/**
 * 新增题库
 * @param data 题库数据
 */
export function addQuestionBank(data: any) {
  return request({
    url: "/gkadmin/v1/q_banks",
    method: "post",
    data: data,
  });
}

/**
 * 修改题库
 * @param id 题库ID
 * @param data 题库数据
 */
export function updateQuestionBank(id: any, data: any) {
  return request({
    url: `/gkadmin/v1/q_banks/${id}`,
    method: "put",
    data: data,
  });
}

/**
 * 删除题库
 * @param id 题库ID
 */
export function deleteQuestionBank(id: any) {
  return request({
    url: `/gkadmin/v1/q_banks/${id}`,
    method: "delete",
  });
}

// 试题管理
/**
 * 获取题目列表
 * @param queryParams 查询参数
 */
export function getQuestions(queryParams?: any) {
  return request({
    url: "/gkadmin/v1/questions",
    method: "get",
    params: queryParams,
  });
}

/**
 * 获取题目详情
 * @param id 题目ID
 */
export function getQuestionDetail(id: any) {
  return request({
    url: `/gkadmin/v1/questions/${id}`,
    method: "get",
  });
}

/**
 * 新增题目
 * @param data 题目数据
 */
export function addQuestion(data: any) {
  return request({
    url: "/gkadmin/v1/questions",
    method: "post",
    data: data,
  });
}

/**
 * 修改题目
 * @param id 题目ID
 * @param data 题目数据
 */
export function updateQuestion(id: any, data: any) {
  return request({
    url: `/gkadmin/v1/questions/${id}`,
    method: "put",
    data: data,
  });
}

/**
 * 删除题目
 * @param id 题目ID
 */
export function deleteQuestion(id: any) {
  return request({
    url: `/gkadmin/v1/questions/${id}`,
    method: "delete",
  });
}

// 考试管理
/**
 * 获取考试列表
 * @param queryParams 查询参数
 */
export function getExams(queryParams?: any) {
  return request({
    url: "/gkadmin/v1/exams",
    method: "get",
    params: queryParams,
  });
}

/**
 * 获取考试详情
 * @param id 考试ID
 */
export function getExamDetail(id: any) {
  return request({
    url: `/gkadmin/v1/exams/${id}`,
    method: "get",
  });
}

/**
 * 新增考试
 * @param data 考试数据
 */
export function addExam(data: any) {
  return request({
    url: "/gkadmin/v1/exams",
    method: "post",
    data: data,
  });
}

/**
 * 修改考试
 * @param id 考试ID
 * @param data 考试数据
 */
export function updateExam(id: any, data: any) {
  return request({
    url: `/gkadmin/v1/exams/${id}`,
    method: "put",
    data: data,
  });
}

/**
 * 删除考试
 * @param id 考试ID
 */
export function deleteExam(id: any) {
  return request({
    url: `/gkadmin/v1/exams/${id}`,
    method: "delete",
  });
}

//试卷管理
/**
 * 获取试卷列表
 * @param queryParams 查询参数
 */
export function getExamPapers(queryParams?: any) {
  return request({
    url: "/gkadmin/v1/exam_papers",
    method: "get",
    params: queryParams,
  });
}

/**
 * 获取试卷详情
 * @param id 试卷ID
 */
export function getExamPaperDetail(id: any) {
  return request({
    url: `/gkadmin/v1/exam_papers/${id}`,
    method: "get",
  });
}

/**
 * 新增试卷
 * @param data 试卷数据
 */
export function addExamPaper(data: any) {
  return request({
    url: "/gkadmin/v1/exam_papers",
    method: "post",
    data: data,
  });
}

/**
 * 修改试卷
 * @param id 试卷ID
 * @param data 试卷数据
 */
export function updateExamPaper(id: any, data: any) {
  return request({
    url: `/gkadmin/v1/exam_papers/${id}`,
    method: "put",
    data: data,
  });
}

/**
 * 删除试卷
 * @param id 试卷ID
 */
export function deleteExamPaper(id: any) {
  return request({
    url: `/gkadmin/v1/exam_papers/${id}`,
    method: "delete",
  });
}

//学员考试记录管理
/**
 * 获取学员考试记录列表
 * @param queryParams 查询参数
 */
export function getUserExams(queryParams?: any) {
  return request({
    url: "/gkadmin/v1/user_exams",
    method: "get",
    params: queryParams,
  });
}

/**
 * 获取学员考试记录详情
 * @param id 考试记录ID
 */
export function getUserExamDetail(id: any) {
  return request({
    url: `/gkadmin/v1/user_exams/${id}`,
    method: "get",
  });
}

/**
 * 重置学员考试记录
 * @param data 考试记录数据
 * reset_result	-	Boolean	是否重置学员考试成绩
 */
// export function updateUserExam(id: any, data: any) {
//   return request({
//     url: `/gkadmin/v1/user_exams/${id}`,
//     method: "put",
//     data: data,
//   });
// }

// 重置学员考试成绩
export function resetUserExam(data: any) {
  return request({
    url: `/gkadmin/v1/user_exams/reset`,
    method: "post",
    data: data,
  });
}

/**
 * 下载模板文件
 * @param queryParams 查询参数（模板文件的名字）
 * @returns Promise<any>
 */
export function downloadTemplate(params?: any): Promise<any> {
  return request({
    url: `/gkadmin/v1/templates/${params}`,
    method: "get",
    responseType: "blob",
    // params: queryParams,
  });
}
// 导入题目
export function questionsImport(data: any) {
  const formData = new FormData();
  formData.append("file", data.file); // 添加文件
  formData.append("q_bank_id", data.q_bank_id);
  return request({
    url: `/gkadmin/v1/questions_import`,
    method: "post",
    data: formData,
    headers: {
      "Content-Type": data.file.type,
    },
  });
}
