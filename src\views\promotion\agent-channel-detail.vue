<template>
  <div class="agent-channel-detail-container">
    <div class="content-header">
      <div class="left">代理渠道-渠道：{{ channelDetail.name }}</div>
      <div class="right">
        <div class="btn primary-btn" @click="handleBack">返回</div>
      </div>
    </div>
    <div class="data-header">
      <div class="order-item">
        <div class="top">会员订单</div>
        <div class="bottom">
          <div class="text-item">
            <div class="num" style="color: #00918c">
              {{ channelDetail?.vip?.total_orders }}
            </div>
            <div class="text">订单数量</div>
          </div>
          <div class="text-item">
            <div class="num" style="color: #fa8c40">
              {{ (channelDetail?.vip?.total_amount || 0) / 100 }}
            </div>
            <div class="text">累计收益</div>
          </div>
        </div>
      </div>
      <div class="order-item">
        <div class="top">课程订单</div>
        <div class="bottom">
          <div class="text-item">
            <div class="num" style="color: #00918c">
              {{ channelDetail?.course?.total_orders }}
            </div>
            <div class="text">订单数量</div>
          </div>
          <div class="text-item">
            <div class="num" style="color: #fa8c40">
              {{ (channelDetail?.course?.total_amount || 0) / 100 }}
            </div>
            <div class="text">累计收益</div>
          </div>
        </div>
      </div>
    </div>
    <div class="table-container">
      <div class="container-header">
        <div class="left">
          <div class="filter-row">
            <el-select
              :suffix-icon="`CaretBottom`"
              size="large"
              v-model="queryParams.status"
              placeholder="订单状态"
              filterable
              clearable
              @change="handleQuery"
            >
              <el-option
                v-for="item in statusOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </div>
          <div class="filter-row">
            <el-date-picker
              class="date-picker"
              size="large"
              v-model="queryParams.dateTimeRange"
              type="daterange"
              range-separator="~"
              start-placeholder="开始时间"
              end-placeholder="结束时间"
              value-format="YYYY-MM-DD"
              @change="handleQuery"
            />
          </div>
          <div class="filter-row">
            <el-input
              v-model="queryParams.mobile"
              placeholder="请输入手机号码"
              size="large"
              clearable
            />
          </div>
          <div class="btn primary-btn" @click="handleQuery">
            <i-ep-search /> 搜索
          </div>
        </div>
        <div class="right">
          <div class="btn text-btn" @click="handleExport">
            <svg-icon class="svg-icon" icon-class="share" />
            导出
          </div>
        </div>
      </div>
      <div class="content">
        <el-table
          v-loading="loading"
          element-loading-text="Loading"
          element-loading-background="#ffffffb4"
          :data="tableData"
          height="100%"
          border
          fit
          highlight-current-row
        >
          <el-table-column label="序号" align="center" min-width="40">
            <template #default="scope">
              {{ scope.$index + 1 }}
            </template>
          </el-table-column>
          <el-table-column label="订单号" align="center" min-width="120">
            <template #default="scope">
              {{ scope.row.order_no }}
            </template>
          </el-table-column>
          <el-table-column label="总金额（￥）" align="center" min-width="80">
            <template #default="scope">
              {{ scope.row.discount_amount || scope.row.total_amount }}
            </template>
          </el-table-column>

          <el-table-column label="订单状态" align="center" min-width="50">
            <template #default="scope">
              <el-tag :type="statusMap[scope.row.status]?.type">{{
                statusMap[scope.row.status]?.label
              }}</el-tag>
            </template>
          </el-table-column>
          <el-table-column label="支付方式" align="center" min-width="50">
            <template #default="scope">
              <el-tag
                v-if="scope.row.pay_way"
                :type="payWayMap[scope.row.pay_way]?.type"
                effect="plain"
                >{{ payWayMap[scope.row.pay_way]?.label }}</el-tag
              >
              <span v-else> - -</span>
            </template>
          </el-table-column>
          <el-table-column
            label="备注"
            align="center"
            min-width="100"
            show-overflow-tooltip
          >
            <template #default="scope">
              {{ scope.row.remark }}
            </template>
          </el-table-column>
          <el-table-column
            label="创建时间"
            align="center"
            min-width="100"
            show-overflow-tooltip
          >
            <template #default="scope">
              {{ scope.row.created_at }}
            </template>
          </el-table-column>
          <el-table-column label="操作" align="center" min-width="80">
            <template #default="scope">
              <div class="option-btn">
                <div
                  class="btn primary-btn"
                  @click="onRowClick('detail', scope.row)"
                >
                  详情
                </div>
              </div>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <div class="footer">
        <pagination
          v-if="total > 0"
          v-model:total="total"
          v-model:page="queryParams.pageNum"
          v-model:limit="queryParams.pageSize"
          @pagination="getOrdersData"
        />
      </div>
    </div>
    <el-dialog
      class="orderDialog"
      v-model="orderDialog.visible"
      append-to-body
      :width="orderDialog.width"
      :title="orderDialog.title"
      @close="closeDialog"
    >
      <div
        class="dialog-body"
        v-loading="dialogLoading"
        element-loading-text="Loading"
        element-loading-background="#ffffffb4"
      >
        <el-scrollbar style="height: 100%" warp-style="overflow-x: hidden;">
          <div class="base-content block">
            <el-row>
              <el-col :span="16" class="base-item">
                <div class="title">基本信息</div>
              </el-col>

              <el-col :span="8" class="base-item">
                <div class="label">订单状态:</div>
                <div
                  class="text"
                  :style="{ color: statusMap[orderDetail.status]?.color }"
                >
                  {{ statusMap[orderDetail.status]?.label || "--" }}
                </div>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="8" class="base-item">
                <div class="label">用户:</div>
                <div class="text">
                  {{
                    orderDetail.user?.name || "绿知学员" + orderDetail.user?.id
                  }}
                </div>
              </el-col>
              <el-col :span="8" class="base-item">
                <div class="label">手机号码:</div>
                <div class="text">{{ orderDetail.user?.mobile || "--" }}</div>
              </el-col>
              <el-col :span="8" class="base-item">
                <div class="label">订单号:</div>
                <div class="text">{{ orderDetail.order_no }}</div>
              </el-col>
            </el-row>

            <el-row>
              <el-col :span="8" class="base-item">
                <div class="label">支付方式:</div>
                <div class="text">
                  {{ payWayMap[orderDetail.pay_way]?.label || "--" }}
                </div>
              </el-col>
              <el-col :span="8" class="base-item">
                <div class="label">数额:</div>
                <div class="text">
                  {{
                    orderDetail.pay_way !== null
                      ? orderDetail.pay_way == 10
                        ? "￥" +
                          (orderDetail.discount_amount ||
                            orderDetail.total_amount)
                        : orderDetail.total_gb + "绿豆"
                      : "--"
                  }}
                </div>
              </el-col>
              <el-col :span="8" class="base-item">
                <div class="label">支付时间:</div>
                <div class="text">
                  {{ orderDetail.paid_at || "--" }}
                </div>
              </el-col>
            </el-row>
          </div>

          <div
            class="pay-content block"
            v-if="orderDetail.pay_way == 10"
            :style="{
              height: orderDetail.pay_way == 10 ? '30%' : '0',
            }"
          >
            <div class="title" v-if="orderDetail.pay_way == 10">
              微信支付信息
            </div>
            <el-row>
              <el-col :span="8" class="pay-item">
                <div class="label">支付订单号：</div>
                <div class="text">
                  {{ wechatPaysItems[0]?.third_payment_no }}
                </div>
              </el-col>
              <el-col :span="8" class="pay-item">
                <div class="label">微信支付单号：</div>
                <div class="text">
                  {{ wechatPaysItems[0]?.transaction_id || "--" }}
                </div>
              </el-col>
              <el-col :span="8" class="pay-item">
                <div class="label">业务结果：</div>
                <div class="text">
                  {{ wechatPaysItems[0]?.result_code || "--" }}
                </div>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="8" class="pay-item">
                <div class="label">付款银行：</div>
                <div class="text">{{ wechatPaysItems[0]?.bank_type }}</div>
              </el-col>
              <el-col :span="8" class="pay-item">
                <div class="label">交易类型：</div>
                <div class="text">{{ wechatPaysItems[0]?.trade_type }}</div>
              </el-col>
              <el-col :span="8" class="pay-item">
                <div class="label">货币种类：</div>
                <div class="text">{{ wechatPaysItems[0]?.fee_type }}</div>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="8" class="pay-item">
                <div class="label">付款金额：</div>
                <div class="text">{{ wechatPaysItems[0]?.amount }}</div>
              </el-col>
              <el-col :span="8" class="pay-item">
                <div class="label">订单金额：</div>
                <div class="text">{{ wechatPaysItems[0]?.total_fee }}</div>
              </el-col>
              <el-col :span="8" class="pay-item">
                <div class="label">支付完成时间：</div>
                <div class="text">{{ wechatPaysItems[0]?.time_end }}</div>
              </el-col>
            </el-row>
          </div>

          <div class="title">
            订单明细项列表
            <span v-if="orderDetail.promotion_theme?.length" class="theme-tips"
              >备注：{{ orderDetail.promotion_theme.join("，") }}</span
            >
          </div>
          <div
            class="order-content"
            v-if="orderItems"
            :style="{
              height: orderDetail.pay_way == 10 ? '40%' : '70%',
            }"
          >
            <el-table
              :data="orderItems"
              height="100%"
              border
              fit
              highlight-current-row
            >
              <el-table-column label="名称" align="center" min-width="120">
                <template #default="scope">
                  <div class="cover-name">
                    <img
                      :src="
                        scope.row.course
                          ? scope.row.thumb
                          : scope.row.item_name == '购买会员'
                          ? vip_img
                          : bean_img
                      "
                      alt=""
                      :class="{
                        'cover-img':
                          scope.row.course || scope.row.item_name == '购买会员',
                        'cover-img2': scope.row.bean,
                      }"
                    />
                    <span>{{ scope.row.item_name }}</span>
                  </div>
                </template>
              </el-table-column>

              <el-table-column label="金额（￥）" align="center" min-width="60">
                <template #default="scope">
                  {{ scope.row.discount_amount || scope.row.amount }}
                </template>
              </el-table-column>
              <el-table-column label="绿豆" align="center" min-width="60">
                <template #default="scope">
                  {{ scope.row.gb_amount }}
                </template>
              </el-table-column>
              <el-table-column label="数量" align="center" min-width="60">
                <template #default="scope">
                  {{ scope.row.quantity }}
                </template>
              </el-table-column>
            </el-table>
          </div>
        </el-scrollbar>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <div class="btn primary-btn" @click="closeDialog">确 定</div>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { useAppStore } from "@/store/modules/app";
import { useRoute, useRouter } from "vue-router";
import * as XLSX from "xlsx";
import { saveAs } from "file-saver";
import { getAgentChannelDetail } from "@/api/promotion/";
import { getOrders, getOrdersDetail } from "@/api/order";
import { parseTime, formatStringDate } from "@/utils";
//const { proxy } = getCurrentInstance();
// proxy 替代 this
defineOptions({
  name: "AgentChannelDetail",
  inheritAttrs: false,
});
const store = useAppStore();
const route = useRoute();
const router = useRouter();

const channelId = ref<any>(route.params.id || route.query.id);
const channelDetail = reactive<any>({});
const loading = ref(false);
const queryParams = reactive<any>({
  search: "",
  pageNum: 1,
  pageSize: 20,
});
const total = ref(0); // 数据总数
const tableData = ref([]);
const payWayMap = reactive<any>({
  10: { type: "success", label: "微信支付", color: "#2ab7b0" },
  20: { type: "success", label: "绿豆", color: "#2ab7b0" },
});
const statusOptions = ref<any>([
  { value: 10, label: "未支付" },
  { value: 20, label: "已支付" },
  { value: 30, label: "已取消" },
  { value: 40, label: "支付失败" },
]);
const statusMap = reactive<any>({
  10: { type: "primary", label: "未支付", color: "#409eff" },
  20: { type: "success", label: "已支付", color: "#00918c" },
  30: { type: "info", label: "已取消", color: "#909399" },
  40: { type: "danger", label: "支付失败", color: "#f56c6c" },
});
const orderDialog = reactive<any>({
  visible: false,
  title: "订单详情",
  width: "60%",
  type: "detail",
});
const orderDetail = reactive<any>({});
const orderItems = ref<any>([]);
const wechatPaysItems = ref<any>([]);
const dialogLoading = ref<any>(false);
const bean_img = ref(new URL("@/assets/images/bean.png", import.meta.url).href);
const vip_img = new URL("@/assets/images/vip-img.png", import.meta.url).href;

watch(
  () => queryParams.dateTimeRange,
  (newVal) => {
    if (newVal) {
      queryParams.from = parseTime(newVal[0], "{y}{m}{d}000000");
      queryParams.to = parseTime(newVal[1], "{y}{m}{d}235959");
    }
  },
  { deep: true }
);
onBeforeMount(() => {});
onMounted(() => {
  getChannelDetail();
  getOrdersData();
});

function getChannelDetail() {
  getAgentChannelDetail(channelId.value).then((res: any) => {
    Object.assign(channelDetail, res.data);
  });
}
function getOrdersData() {
  loading.value = true;
  const params = {
    search: queryParams.search || undefined,
    mobile: queryParams.mobile || undefined,
    page: queryParams.pageNum,
    per_page: queryParams.pageSize,
    from: queryParams.from || undefined,
    to: queryParams.to || undefined,
    // pay_way: 10,
    gk_channel_code_id: channelId.value,
    status: queryParams.status || undefined,
  };
  if (!queryParams.dateTimeRange) {
    delete params.from;
    delete params.to;
  }
  getOrders(params).then((res: any) => {
    if (res.status == 200) {
      tableData.value = res.data.orders.map((item: any) => {
        item.created_at = parseTime(item.created_at, "{y}-{m}-{d} {h}:{i}:{s}");
        item.paid_at = item.paid_at
          ? parseTime(item.paid_at, "{y}-{m}-{d} {h}:{i}:{s}")
          : "--";
        item.discount_amount = item?.discount_amount / 100;
        item.total_amount = item.total_amount / 100;
        return item;
      });
      total.value = res.total;
      loading.value = false;
    }
  });
}

function onRowClick(type: any, row: any) {
  if (type === "detail") {
    dialogLoading.value = true;
    orderDialog.visible = true;
    orderDialog.type = "detail";
    getOrdersDetail(row.id)
      .then((res: any) => {
        if (res.status == 200) {
          Object.assign(orderDetail, res.data, {
            created_at: parseTime(
              res.data.created_at,
              "{y}-{m}-{d} {h}:{i}:{s}"
            ),
            paid_at: res.data.paid_at
              ? parseTime(res.data.paid_at, "{y}-{m}-{d} {h}:{i}:{s}")
              : "--",
            discount_amount: res.data?.discount_amount / 100,
            total_amount: res.data.total_amount / 100,
          });

          orderItems.value = res.data.items.map((item: any) => {
            item.discount_amount = item?.discount_amount / 100;
            item.amount = item.amount / 100;
            return item;
          });
          wechatPaysItems.value = res.data.wechat_pays.map((item: any) => {
            item.amount = "￥" + item.amount / 100;
            item.total_fee = "￥" + item.total_fee / 100;
            item.created_at = parseTime(
              item.created_at,
              "{y}-{m}-{d} {h}:{i}:{s}"
            );

            item.time_end = formatStringDate(
              item.time_end,
              "{y}-{m}-{d} {h}:{i}:{s}"
            );
            return item;
          });
          dialogLoading.value = false;
        }
      })
      .catch((e) => {
        dialogLoading.value = false;
      });
  }
}

function closeDialog() {
  orderDialog.visible = false;
  orderItems.value = [];
  wechatPaysItems.value = [];
}

function handleQuery() {
  queryParams.pageNum = 1;
  getOrdersData();
}

//导出数据
function handleExport() {
  if (!tableData.value || tableData.value.length === 0) {
    ElMessage.warning("当前没有可导出的数据");
    return;
  }

  try {
    const data = tableData.value.map((row: any, index: any) => ({
      序号: index + 1,
      订单号: row.order_no,
      "总金额（￥）": row.discount_amount || row.total_amount,
      订单状态: statusMap[row.status]?.label,
      支付方式: payWayMap[row.pay_way]?.label,
      备注: row.remark,
      创建时间: row.created_at,
    }));

    // 生成工作表
    const ws = XLSX.utils.json_to_sheet(data);

    // 设置所有单元格内容居中（ SheetJS xlsx 社区版插件不支持？）
    const range = XLSX.utils.decode_range(ws["!ref"]!);
    for (let R = range.s.r; R <= range.e.r; ++R) {
      for (let C = range.s.c; C <= range.e.c; ++C) {
        const cell_address = XLSX.utils.encode_cell({ r: R, c: C });
        if (!ws[cell_address]) continue;
        if (!ws[cell_address].s) ws[cell_address].s = {};
        ws[cell_address].s.alignment = {
          horizontal: "center",
          vertical: "center",
        };
      }
    }

    // 设置列宽自适应，取表头和内容最大长度
    const colWidths = Object.keys(data[0]).map((key, colIdx) => {
      // 表头长度
      const headerLen = String(key).length;
      // 内容最大长度
      const contentLen = Math.max(
        ...data.map((row) => String(row[key] ?? "").length)
      );
      const maxLen = Math.max(headerLen, contentLen);
      return { wch: maxLen + 2 }; // +2 适当留白
    });
    ws["!cols"] = colWidths;

    // 创建工作簿并导出
    const wb = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(wb, ws, "代理渠道数据");

    // 生成 Excel 文件并触发下载
    const excelBuffer = XLSX.write(wb, {
      bookType: "xlsx",
      type: "array",
      cellStyles: true,
    });
    const blob = new Blob([excelBuffer], {
      type: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
    });

    saveAs(blob, `代理渠道-${channelDetail.name}-数据.xlsx`);
    ElMessage.success("导出成功！");
  } catch (error) {
    console.error("导出失败:", error);
    ElMessage.error("导出失败，请稍后重试");
  }
}
function handleBack() {
  router.go(-1);
}
</script>

<style scoped lang="scss">
.agent-channel-detail-container {
  // width: 50%;
  height: 95%;
  margin: 20px;

  .content-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    height: 76px;
    padding: 10px 20px;
    margin-bottom: 15px;
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 3px 20px 1px rgb(109 125 126 / 7%);

    .left {
      font-size: 24px;
      font-weight: 500;
      color: #3b4664;
    }

    .btn {
      width: 52px;
      height: 28px;
      border-radius: 13px;
    }
  }

  .data-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    margin-bottom: 15px;

    .order-item {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      width: 49%;
      background: #fff;

      .top {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 100%;
        height: 81px;
        font-size: 40px;
        font-weight: 500;
        color: #3b4664;
        background: url("@/assets/promotion/header-tag2.png") no-repeat;
        background-size: 100% 100%;
      }

      .bottom {
        display: flex;
        align-items: center;
        justify-content: space-between;
        width: 100%;
        height: 164px;

        .text-item {
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          width: 50%;
          border-right: 1px solid #b2c6c6;

          .num {
            font-size: 50px;
            font-weight: normal;
            color: #00918c;
          }

          .text {
            font-size: 24px;
            font-weight: 400;
            color: #3b4664;
          }
        }
      }
    }
  }

  .table-container {
    width: 100%;
    height: calc(100% - 340px);
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 3px 20px 1px rgb(109 125 126 / 7%);

    .container-header {
      display: flex;
      justify-content: space-between;
      width: 100%;
      padding: 20px;

      .left {
        display: flex;
        align-items: center;
        justify-content: space-between;
        // width: 60%;
        .btn {
          width: 116px;
          height: 40px;
          margin-left: 20px;
        }
      }

      .right {
        display: flex;
        align-items: center;
        justify-content: center;

        .btn {
          width: 120px;
          height: 38px;
          color: #fff;
          background: linear-gradient(180deg, #00918c 0%, #1c8d84 100%);
          border-radius: 20px;
          box-shadow: inset 0 3px 6px 1px rgb(224 252 237 / 37%);

          svg {
            margin-right: 5px;
          }
        }
      }

      .filter-row {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 260px;
        margin-left: 20px;

        &:nth-child(1) {
          width: 260px;
          margin-left: 0;
        }

        .btn {
          width: 116px;
          height: 40px;
          margin-left: 20px;
        }
      }
    }

    .content {
      width: 100%;
      height: calc(100% - 170px);
      padding: 10px 20px;

      .cover-name {
        img {
          object-fit: cover;
        }
      }
    }

    .footer {
      display: flex;
      align-items: center;
      justify-content: center;
      margin-top: 15px;
    }
  }
}
</style>
<style lang="scss">
.orderDialog {
  padding: 20px 10px;

  .dialog-body {
    display: flex;
    // justify-content: space-around;
    flex-direction: column;
    height: 70vh;
  }

  .title {
    display: flex;
    align-items: center;
    font-size: 20px;
    // padding: 10px 0;
    font-weight: 500;
    color: #3d4040;

    .theme-tips {
      margin-left: 10px;
      font-size: 14px;
      font-weight: 400;
      color: #e0a07a;
    }
  }

  .block {
    margin-bottom: 15px;
    background: linear-gradient(180deg, #fff 0%, #fafdfb 100%);
    border-radius: 8px;
    box-shadow: inset 0 0 50px 1px rgb(177 239 239 / 31%);
  }

  .base-content {
    height: 30%;
    padding: 5px 20px;

    .el-row {
      margin: 15px 0;
    }

    .base-item {
      display: flex;
      align-items: center;

      .label {
        font-size: 17px;
        font-weight: 400;
        color: #3b4664;
      }

      .text {
        margin-left: 10px;
        font-size: 17px;
        font-weight: 500;
        color: #3b4664;
      }
    }
  }

  .pay-content {
    height: 30%;
    padding: 5px 20px;

    .el-row {
      margin: 15px 0;
    }

    .pay-item {
      display: flex;
      align-items: center;

      .label {
        font-size: 17px;
        font-weight: 400;
        color: #3b4664;
      }

      .text {
        margin-left: 10px;
        font-size: 17px;
        font-weight: 500;
        color: #3b4664;
      }
    }
  }

  .order-content {
    height: 40%;
    margin-top: 10px;

    .cover-name {
      display: flex;
      align-items: center;
      justify-content: center;

      .cover-img {
        //width: 50%;
        //height: 126px;
        width: 162px;
        height: 86px;
        object-fit: cover;
        border-radius: 8px;
      }

      .cover-img2 {
        // width: 30%;
        // height: 126px;
        width: 162px;
        height: 86px;
        object-fit: contain;
        border-radius: 8px;
      }

      span {
        display: inline-block;
        width: 40%;
      }
    }
  }
}
</style>
