<template>
  <div class="personal-order-container">
    <div class="container-header">
      <div class="left">
        <!-- <div class="filter-row">
          <el-input
            v-model="queryParams.search"
            placeholder="请输入订单号"
            size="large"
          />
        </div> -->

        <div class="filter-row">
          <el-select
            :suffix-icon="`CaretBottom`"
            size="large"
            v-model="queryParams.pay_way"
            placeholder="请选择付款方式"
            filterable
            clearable
            @change="handleQuery"
          >
            <el-option
              v-for="item in payWayOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </div>
        <div class="filter-row">
          <el-select
            :suffix-icon="`CaretBottom`"
            size="large"
            v-model="queryParams.status"
            placeholder="订单状态"
            filterable
            clearable
            @change="handleQuery"
          >
            <el-option
              v-for="item in statusOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </div>
        <div class="filter-row">
          <el-date-picker
            class="date-picker"
            size="large"
            v-model="queryParams.dateTimeRange"
            type="daterange"
            range-separator="~"
            start-placeholder="开始时间"
            end-placeholder="结束时间"
            value-format="YYYY-MM-DD"
            @change="handleQuery"
          />
        </div>
        <div class="filter-row">
          <el-input
            v-model="queryParams.mobile"
            placeholder="请输入手机号码"
            size="large"
          />
        </div>

        <div class="filter-row">
          <div class="btn primary-btn" @click="handleQuery">
            <i-ep-search /> 搜索
          </div>
        </div>
      </div>
      <!-- <div class="right"></div> -->
    </div>
    <div class="content">
      <el-table
        v-loading="loading"
        element-loading-text="Loading"
        element-loading-background="#ffffffb4"
        :data="tableData"
        height="100%"
        border
        fit
        highlight-current-row
      >
        <el-table-column label="ID" align="center" min-width="40">
          <template #default="scope">
            {{ scope.row.id }}
          </template>
        </el-table-column>
        <el-table-column label="订单号" align="center" min-width="120">
          <template #default="scope">
            {{ scope.row.order_no }}
          </template>
        </el-table-column>
        <el-table-column label="总金额（￥）" align="center" min-width="80">
          <template #default="scope">
            <span v-if="scope.row.pay_way">
              {{
                scope.row?.pay_way == 10
                  ? scope.row.discount_amount || scope.row.total_amount
                  : "--"
              }}</span
            >
            <span v-else>--</span>
          </template>
        </el-table-column>
        <el-table-column label="总绿豆数" align="center" min-width="80">
          <template #default="scope">
            <span v-if="scope.row.pay_way">
              {{ scope.row?.pay_way == 20 ? scope.row.total_gb : "--" }}</span
            >
            <span v-else>--</span>
          </template>
        </el-table-column>
        <el-table-column label="订单状态" align="center" min-width="50">
          <template #default="scope">
            <el-tag :type="statusMap[scope.row.status]?.type">{{
              statusMap[scope.row.status]?.label
            }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="支付方式" align="center" min-width="50">
          <template #default="scope">
            <el-tag
              v-if="scope.row.pay_way"
              :type="payWayMap[scope.row.pay_way]?.type"
              effect="plain"
              >{{ payWayMap[scope.row.pay_way]?.label }}</el-tag
            >
            <span v-else>- -</span>
          </template>
        </el-table-column>
        <el-table-column
          label="备注"
          align="center"
          min-width="100"
          show-overflow-tooltip
        >
          <template #default="scope">
            {{ scope.row.remark }}
          </template>
        </el-table-column>
        <el-table-column
          label="创建时间"
          align="center"
          min-width="100"
          show-overflow-tooltip
        >
          <template #default="scope">
            {{ scope.row.created_at }}
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center" min-width="80">
          <template #default="scope">
            <div class="option-btn">
              <div
                class="btn primary-btn"
                @click="onRowClick('detail', scope.row)"
              >
                详情
              </div>
            </div>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <div class="footer">
      <pagination
        v-if="total > 0"
        v-model:total="total"
        v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize"
        @pagination="getData"
      />
    </div>

    <!-- 弹窗 -->
    <el-dialog
      class="dialog"
      v-model="dialog.visible"
      :title="dialog.title"
      :width="dialog.width"
      append-to-body
      @close="closeDialog"
    >
      <div
        class="dialog-body"
        v-loading="dialogLoading"
        element-loading-text="Loading"
        element-loading-background="#ffffffb4"
      >
        <el-scrollbar style="height: 100%" warp-style="overflow-x: hidden;">
          <div class="base-content block">
            <el-row>
              <el-col :span="16" class="base-item">
                <div class="title">基本信息</div>
              </el-col>

              <el-col :span="8" class="base-item">
                <div class="label">订单状态:</div>
                <div
                  class="text"
                  :style="{ color: statusMap[orderDetail.status]?.color }"
                >
                  {{ statusMap[orderDetail.status]?.label || "--" }}
                </div>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="8" class="base-item">
                <div class="label">用户:</div>
                <div class="text">
                  {{
                    orderDetail.user?.name || "绿知学员" + orderDetail.user?.id
                  }}
                </div>
              </el-col>
              <el-col :span="8" class="base-item">
                <div class="label">手机号码:</div>
                <div class="text">{{ orderDetail.user?.mobile || "--" }}</div>
              </el-col>
              <el-col :span="8" class="base-item">
                <div class="label">订单号:</div>
                <div class="text">{{ orderDetail.order_no }}</div>
              </el-col>
            </el-row>

            <el-row>
              <el-col :span="8" class="base-item">
                <div class="label">支付方式:</div>
                <div class="text">
                  {{ payWayMap[orderDetail.pay_way]?.label || "--" }}
                </div>
              </el-col>
              <el-col :span="8" class="base-item">
                <div class="label">数额:</div>
                <div class="text">
                  {{
                    orderDetail.pay_way !== null
                      ? orderDetail.pay_way == 10
                        ? "￥" +
                          (orderDetail.discount_amount ||
                            orderDetail.total_amount)
                        : orderDetail.total_gb + "绿豆"
                      : "--"
                  }}
                </div>
              </el-col>
              <el-col :span="8" class="base-item">
                <div class="label">支付时间:</div>
                <div class="text">
                  {{ orderDetail.paid_at || "--" }}
                </div>
              </el-col>
            </el-row>
          </div>

          <div
            class="pay-content block"
            v-if="orderDetail.pay_way == 10"
            :style="{
              height: orderDetail.pay_way == 10 ? '30%' : '0',
            }"
          >
            <div class="title" v-if="orderDetail.pay_way == 10">
              微信支付信息
            </div>
            <el-row>
              <el-col :span="8" class="pay-item">
                <div class="label">支付订单号：</div>
                <div class="text">
                  {{ wechatPaysItems[0]?.third_payment_no }}
                </div>
              </el-col>
              <el-col :span="8" class="pay-item">
                <div class="label">微信支付单号：</div>
                <div class="text">
                  {{ wechatPaysItems[0]?.transaction_id || "--" }}
                </div>
              </el-col>
              <el-col :span="8" class="pay-item">
                <div class="label">业务结果：</div>
                <div class="text">
                  {{ wechatPaysItems[0]?.result_code || "--" }}
                </div>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="8" class="pay-item">
                <div class="label">付款银行：</div>
                <div class="text">{{ wechatPaysItems[0]?.bank_type }}</div>
              </el-col>
              <el-col :span="8" class="pay-item">
                <div class="label">交易类型：</div>
                <div class="text">{{ wechatPaysItems[0]?.trade_type }}</div>
              </el-col>
              <el-col :span="8" class="pay-item">
                <div class="label">货币种类：</div>
                <div class="text">{{ wechatPaysItems[0]?.fee_type }}</div>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="8" class="pay-item">
                <div class="label">付款金额：</div>
                <div class="text">{{ wechatPaysItems[0]?.amount }}</div>
              </el-col>
              <el-col :span="8" class="pay-item">
                <div class="label">订单金额：</div>
                <div class="text">{{ wechatPaysItems[0]?.total_fee }}</div>
              </el-col>
              <el-col :span="8" class="pay-item">
                <div class="label">支付完成时间：</div>
                <div class="text">{{ wechatPaysItems[0]?.time_end }}</div>
              </el-col>
            </el-row>
          </div>

          <div class="title">
            订单明细项列表
            <span v-if="orderDetail.promotion_theme?.length" class="theme-tips"
              >备注：{{ orderDetail.promotion_theme.join("，") }}</span
            >
          </div>
          <div
            class="order-content"
            v-if="orderItems"
            :style="{
              height: orderDetail.pay_way == 10 ? '40%' : '70%',
            }"
          >
            <el-table
              :data="orderItems"
              height="100%"
              border
              fit
              highlight-current-row
            >
              <el-table-column label="名称" align="center" min-width="120">
                <template #default="scope">
                  <div class="cover-name">
                    <img
                      :src="
                        scope.row.course
                          ? scope.row.thumb
                          : scope.row.item_name == '购买会员'
                          ? vip_img
                          : bean_img
                      "
                      alt=""
                      :class="{
                        'cover-img':
                          scope.row.course || scope.row.item_name == '购买会员',
                        'cover-img2': scope.row.bean,
                      }"
                    />
                    <span>{{ scope.row.item_name }}</span>
                  </div>
                </template>
              </el-table-column>

              <el-table-column label="金额（￥）" align="center" min-width="60">
                <template #default="scope">
                  {{ scope.row.discount_amount || scope.row.amount }}
                </template>
              </el-table-column>
              <el-table-column label="绿豆" align="center" min-width="60">
                <template #default="scope">
                  {{ scope.row.gb_amount }}
                </template>
              </el-table-column>
              <el-table-column label="数量" align="center" min-width="60">
                <template #default="scope">
                  {{ scope.row.quantity }}
                </template>
              </el-table-column>
            </el-table>
          </div>
        </el-scrollbar>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <!-- <div class="btn cancel-btn" @click="closeDialog">取 消</div> -->
          <div class="btn primary-btn" @click="closeDialog">确 定</div>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { useAppStore } from "@/store/modules/app";
import { useRoute, useRouter } from "vue-router";
import { getOrders, getOrdersDetail } from "@/api/order";
import { parseTime, formatStringDate } from "@/utils";
const { proxy } = getCurrentInstance() as any;
// proxy 替代 this
defineOptions({
  name: "PersonalOrder",
  inheritAttrs: false,
});
/** 仓库*/
const store = useAppStore();
/**路由对象*/
const route = useRoute();
/**路由实例*/
const router = useRouter();

const sortOptions = reactive<any>([]);
const loading = ref(false);
// const removeIds = ref([]); // 删除用户ID集合 用于批量删除
const queryParams = reactive<any>({
  pageNum: 1,
  pageSize: 20,
  dateTimeRange: [],
  from: "",
  to: "",
  status: "",
  mobile: "",
  pay_way: "",
});
const payWayOptions = ref<any>([
  { value: 10, label: "金额" },
  {
    value: 20,
    label: "绿豆",
  },
]);
const payWayMap = reactive<any>({
  10: { type: "success", label: "微信支付", color: "#2ab7b0" },
  20: { type: "success", label: "绿豆", color: "#2ab7b0" },
  // add more status mappings as needed
});
const statusOptions = ref<any>([
  { value: 10, label: "未支付" },
  { value: 20, label: "已支付" },
  { value: 30, label: "已取消" },
  { value: 40, label: "支付失败" },
]);
const statusMap = reactive<any>({
  10: { type: "primary", label: "未支付", color: "#409eff" },
  20: { type: "success", label: "已支付", color: "#00918c" },
  30: { type: "info", label: "已取消", color: "#909399" },
  40: { type: "danger", label: "支付失败", color: "#f56c6c" },
  // add more status mappings as needed
});

const total = ref(0); // 数据总数
const tableData = ref<any>([]);
const bean_img = ref(new URL("@/assets/images/bean.png", import.meta.url).href);
const vip_img = new URL("@/assets/images/vip-img.png", import.meta.url).href;
// 弹窗对象
const dialog = reactive({
  visible: false,
  type: "form",
  width: "65%",
  title: "详情",
});

// 用户表单数据
const rowId = ref();
const formData = reactive<any>({});
const orderDetail = reactive<any>({});
const orderItems = ref<any>([]);
const wechatPaysItems = ref<any>([]);
const dialogLoading = ref<any>(false);
//   [
//   {
//     payment_order_no: "123131", //支付订单号
//     amount: "123123", //		支付金额，单位：分
//     // third_payment_no: "", //
//     // result_code: "", //			业务结果
//     // err_code: "", //		错误代码
//     // err_code_desc: "", //			err_code_des，错误描述
//     trade_type: "", //			交易类型
//     bank_type: "231", //			付款银行
//     total_fee: "123", //			订单金额
//     fee_type: "213", //货币种类，如：CNY
//     transaction_id: "123", //		微信支付订单号
//     time_end: "3312", //		支付完成时间
//   },
// ]

watch(
  () => queryParams.dateTimeRange,
  (newVal) => {
    if (newVal) {
      queryParams.from = parseTime(newVal[0], "{y}{m}{d}000000");
      queryParams.to = parseTime(newVal[1], "{y}{m}{d}235959");
    }
  },
  { deep: true }
);
onMounted(() => {
  getData();
});

function getData() {
  loading.value = true;
  const params = {
    search: queryParams.search || undefined,
    page: queryParams.pageNum,
    per_page: queryParams.pageSize,
    from: queryParams.from || undefined,
    to: queryParams.to || undefined,
    status: queryParams.status || undefined,
    mobile: queryParams.mobile || undefined,
    pay_way: queryParams.pay_way || undefined,
  };
  if (!queryParams.dateTimeRange) {
    delete params.from;
    delete params.to;
  }
  getOrders(params).then((res: any) => {
    if (res.status == 200) {
      tableData.value = res.data.orders.map((item: any) => {
        item.created_at = parseTime(item.created_at, "{y}-{m}-{d} {h}:{i}:{s}");
        item.paid_at = item.paid_at
          ? parseTime(item.paid_at, "{y}-{m}-{d} {h}:{i}:{s}")
          : "--";
        item.discount_amount = item?.discount_amount / 100;
        item.total_amount = item.total_amount / 100;
        return item;
      });
      total.value = res.total;
      loading.value = false;
    }
  });
}

function onRowClick(type: string, row: any) {
  if (type === "detail") {
    dialogLoading.value = true;
    rowId.value = row.id;
    dialog.title = "订单详情";
    dialog.type = "detail";
    // Object.assign(formData, row);
    getOrdersDetail(row.id)
      .then((res: any) => {
        if (res.status == 200) {
          dialog.visible = true;
          Object.assign(orderDetail, res.data, {
            created_at: parseTime(
              res.data.created_at,
              "{y}-{m}-{d} {h}:{i}:{s}"
            ),
            paid_at: res.data.paid_at
              ? parseTime(res.data.paid_at, "{y}-{m}-{d} {h}:{i}:{s}")
              : "--",
            discount_amount: res.data?.discount_amount / 100,
            total_amount: res.data.total_amount / 100,
          });

          orderItems.value = res.data.items.map((item: any) => {
            item.discount_amount = item?.discount_amount / 100;
            item.amount = item.amount / 100;
            return item;
          });
          wechatPaysItems.value = res.data.wechat_pays.map((item: any) => {
            item.amount = "￥" + item.amount / 100;
            item.total_fee = "￥" + item.total_fee / 100;
            item.created_at = parseTime(
              item.created_at,
              "{y}-{m}-{d} {h}:{i}:{s}"
            );
            // console.log("item.time_end", formatStringDate(item.time_end));
            item.time_end = formatStringDate(
              item.time_end,
              "{y}-{m}-{d} {h}:{i}:{s}"
            );
            return item;
          });
          dialogLoading.value = false;
        }
      })
      .catch((e) => {
        dialogLoading.value = false;
      });
  }
}

function handleQuery() {
  queryParams.pageNum = 1;
  getData();
}

function closeDialog() {
  dialog.visible = false;
  orderItems.value = [];
  wechatPaysItems.value = [];
}
</script>

<style scoped lang="scss">
.personal-order-container {
  // width: 50%;
  height: 95%;
  margin: 20px;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 3px 20px 1px rgb(109 125 126 / 7%);

  .container-header {
    display: flex;
    justify-content: space-between;
    width: 100%;
    padding: 20px;

    .left {
      display: flex;
      align-items: center;
      justify-content: space-between;
      // width: 100%;
    }
    // .right {
    //   display: flex;
    //   justify-content: center;
    //   align-items: center;
    // }
    .filter-row {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 260px;
      margin-left: 20px;

      &:nth-child(1) {
        margin-left: 0;
      }

      &:last-child {
        width: 15%;
        margin-left: 0;
      }

      .btn {
        width: 116px;
        height: 40px;
        margin-left: 20px;
      }
    }
  }

  .content {
    width: 100%;
    height: calc(100% - 170px);
    padding: 10px 20px;
  }

  .footer {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-top: 15px;
  }
}

.dialog {
  padding: 20px 10px;

  .dialog-body {
    display: flex;
    // justify-content: space-around;
    flex-direction: column;
    height: 70vh;
  }

  .title {
    display: flex;
    align-items: center;
    font-size: 20px;
    // padding: 10px 0;
    font-weight: 500;
    color: #3d4040;

    .theme-tips {
      margin-left: 10px;
      font-size: 14px;
      font-weight: 400;
      color: #e0a07a;
    }
  }

  .block {
    margin-bottom: 15px;
    background: linear-gradient(180deg, #fff 0%, #fafdfb 100%);
    border-radius: 8px;
    box-shadow: inset 0 0 50px 1px rgb(177 239 239 / 31%);
  }

  .base-content {
    height: 30%;
    padding: 5px 20px;

    .el-row {
      margin: 15px 0;
    }

    .base-item {
      display: flex;
      align-items: center;

      .label {
        font-size: 17px;
        font-weight: 400;
        color: #3b4664;
      }

      .text {
        margin-left: 10px;
        font-size: 17px;
        font-weight: 500;
        color: #3b4664;
      }
    }
  }

  .pay-content {
    height: 30%;
    padding: 5px 20px;

    .el-row {
      margin: 15px 0;
    }

    .pay-item {
      display: flex;
      align-items: center;

      .label {
        font-size: 17px;
        font-weight: 400;
        color: #3b4664;
      }

      .text {
        margin-left: 10px;
        font-size: 17px;
        font-weight: 500;
        color: #3b4664;
      }
    }
  }

  .order-content {
    height: 40%;
    margin-top: 10px;

    .cover-name {
      display: flex;
      align-items: center;
      justify-content: center;

      .cover-img {
        // width: 50%;
        // height: 126px;
        width: 162px;
        height: 86px;
        object-fit: cover;
        border-radius: 8px;
      }

      .cover-img2 {
        // width: 30%;
        // height: 126px;
        width: 162px;
        height: 86px;
        object-fit: contain;
        border-radius: 8px;
      }

      span {
        display: inline-block;
        width: 40%;
      }
    }
  }
}
</style>
