import request from "@/utils/request";

export function getPromotionalData(queryParams?: any) {
  return request({
    url: "/gkadmin/v1/promotion_data",
    method: "get",
    params: queryParams,
  });
}

export function getAgentChannels(queryParams?: any) {
  return request({
    url: "/gkadmin/v1/gk_channel_code",
    method: "get",
    params: queryParams,
  });
}
export function getAgentChannelDetail(id: any) {
  return request({
    url: `/gkadmin/v1/gk_channel_code/${id}`,
    method: "get",
  });
}
export function addAgentChannel(data?: any) {
  return request({
    url: "/gkadmin/v1/gk_channel_code",
    method: "post",
    data,
  });
}
export function updateAgentChannel(id: any, data?: any) {
  return request({
    url: `/gkadmin/v1/gk_channel_code/${id}`,
    method: "put",
    data,
  });
}
export function deleteAgentChannel(id?: any) {
  return request({
    url: `/gkadmin/v1/gk_channel_code/${id}`,
    method: "delete",
  });
}
