/* stylelint-disable */
// 自定义按钮的样式 TODO:
.btn {
  cursor: pointer;
  box-shadow: inset 0px 3px 6px 1px rgba(239, 252, 234, 0.44);
  border-radius: 19px 19px 19px 19px;
  text-align: center;
  //font-family: PingFang SC, PingFang SC;
  color: #ffffff;
  justify-content: center;
  align-items: center;
  display: flex;
  user-select: none;
  &:hover {
    opacity: 0.8;
  }
  &:active {
    opacity: 0.5;
  }
}

.text-btn {
  color: #00918c;
  cursor: pointer;
  justify-content: center;
  align-items: center;
  display: flex;
  user-select: none;
  //font-family: 苹方-简, 苹方-简;
  font-weight: 400;
  font-size: 14px;
}
.text-plain-btn {
  cursor: pointer;
  justify-content: center;
  align-items: center;
  display: flex;
  user-select: none;
  background: #ffffff;
  box-shadow: 0px 3px 6px 1px rgba(107, 174, 175, 0.25);
  border-radius: 19px 19px 19px 19px;
  border: 1px solid #6aaeae;
  //font-family: PingFang SC, PingFang SC;
  font-weight: 400;
  font-size: 15px;
  color: #6aaeae;
}
.big {
  width: 130px;
  height: 44px;
  font-size: 18px;
  line-height: 44px;
}

.save {
  background: linear-gradient(180deg, #a6f0b0 0%, #8bc791 100%);
  box-shadow: 0px 3px 10px 1px rgba(29, 162, 131, 0.25),
    inset 0px 3px 10px 1px #c0e5e3;
  border-radius: 35px 35px 35px 35px;
}

.primary-btn,
.edit-btn,
.green-btn {
  background: linear-gradient(180deg, #00918c 0%, #1c8d84 100%);
  border-radius: 2px 2px 2px 2px;
}
.login-btn {
  background: linear-gradient(180deg, #33cec9 0%, #1c8d84 100%);
  box-shadow: 0px 3px 10px 1px rgba(29, 162, 131, 0.25),
    inset 0px 3px 10px 1px rgba(193, 239, 233, 0.4);
}
.save-btn2 {
  background: linear-gradient(180deg, #a6f0b0 0%, #8bc791 100%);
  border-radius: 2px 2px 2px 2px;
}

.add,
.green-btn2 {
  background: linear-gradient(180deg, #00918c 0%, #1c8d84 100%);
  box-shadow: 0px 3px 10px 1px rgba(29, 162, 131, 0.25),
    inset 0px 3px 10px 1px rgba(193, 239, 233, 0.4);
  border-radius: 35px 35px 35px 35px;
}

.green-btn3 {
  background: linear-gradient(180deg, #00918c 0%, #1c8d84 100%);
  box-shadow: 0px 3px 10px 1px rgba(29, 162, 131, 0.25),
    inset 0px 3px 10px 1px rgba(193, 239, 233, 0.4);
  border-radius: 10px 10px 10px 10px;
}

.delete-btn,
.orange-btn {
  background: linear-gradient(
    180deg,
    #e5601a 0%,
    rgba(237, 160, 119, 0.99) 100%
  );
  border-radius: 2px 2px 2px 2px;
}

.info-btn,
.grey-btn {
  background: linear-gradient(180deg, #84a395 0%, #a6bfb0 100%);
  border-radius: 2px 2px 2px 2px;
}

.cancel-btn {
  background: linear-gradient(
    180deg,
    #e5601a 0%,
    rgba(237, 160, 119, 0.99) 100%
  );
  box-shadow: 0px 3px 10px 1px rgba(29, 162, 131, 0.25);
  border-radius: 10px 10px 10px 10px;
}
.light-green-btn {
  background: linear-gradient(180deg, #3ca575 0%, #5ccb8b 100%);
  border-radius: 2px 2px 2px 2px;
}
.light-green-btn2 {
  background: linear-gradient(180deg, #0eb53b 0%, #0ac75b 100%);
  border-radius: 2px 2px 2px 2px;
}
.verify-btn,
.light-blue-btn {
  background: linear-gradient(180deg, #63b0f2 0%, #2d7dcc 100%);
  border-radius: 2px 2px 2px 2px;
}

.deep-blue-btn {
  background: linear-gradient(180deg, #03527c 0%, #2f99f4 100%);
  border-radius: 2px 2px 2px 2px;
}
.recommend-btn {
  background: linear-gradient(180deg, #33cec9 0%, #1c8d84 100%);
  border-radius: 2px 2px 2px 2px;
}
.give-btn,
.yellow-btn {
  background: linear-gradient(180deg, #f0cc09 0%, #8d801c 100%);
  border-radius: 2px 2px 2px 2px;
}

.el-button.is-text {
  box-shadow: 0px 3px 6px 1px rgba(144, 167, 180, 0.34) !important;
  border-radius: 2px 2px 2px 2px !important;
  border: 1px solid #6ed57c !important;
  color: #6ed57c !important;
}
.el-button--primary.el-button--small {
  background: linear-gradient(180deg, #00918c 0%, #1c8d84 100%) !important;
  border-radius: 2px 2px 2px 2px !important;
}

.disabled-btn {
  pointer-events: none;
  opacity: 0.2;
}

// 自定义输入框的样式
.el-input__wrapper,
.el-textarea__inner,
.el-select__wrapper {
  border-radius: 5px !important;
  box-shadow: inset 1px 1px 6px 1px rgba(188, 198, 214, 0.14) !important;
  background-color: #f5f5f513 !important;
  border: 1px solid #c0c7d6 !important;
  font-size: 16px !important;
  color: #3b4664 !important;
  font-family: inherit !important;
  font-weight: 400 !important;
  &::placeholder,
  .is-transparent {
    font-size: 16px !important;
    color: #626c7187 !important;
  }
}

.el-select__input-wrapper,
.el-select__placeholder {
  font-size: 16px !important;
  color: #3b4664 !important;
  font-weight: 400 !important;
  font-family: inherit !important;
  &::placeholder,
  .is-transparent {
    font-size: 16px !important;
    color: #626c7187 !important;
  }
}

.el-input__inner {
  border: none !important;
  font-size: 16px !important;
  color: #3b4664 !important;
  font-weight: 400 !important;
  height: 24px;
  font-family: inherit !important;
}

.el-dialog {
  //所有弹窗居中
  position: absolute;
  display: flex;
  flex-direction: column;
  margin: 0 !important;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  // box-shadow: 6px 3px 20px 1px rgba(89, 152, 115, 0.38),
  //   inset 6px 6px 20px 1px rgba(21, 134, 29, 0.13) !important;
  // border-radius: 25px 25px 25px 25px !important;
  border-radius: 15px 15px 15px 15px !important;
  .el-dialog__body {
    position: relative !important;
  }
  .el-dialog__header {
    text-align: center;
    padding: 10px !important;
    margin-bottom: 10px !important;
    .el-dialog__title {
      //font-family: PingFang SC, PingFang SC;
      font-weight: 400;
      font-size: 22px;
      color: #202123;
    }
  }
  .el-dialog__header.show-close {
    padding: 10px !important;
  }
  .el-dialog__close {
    width: 21px;
    height: 21px;
    background: #c0c7d6;
    border-radius: 50%;
    svg {
      font-size: 20px;
      path {
        fill: #ffff !important;
      }
    }
    &:hover {
      svg {
        path {
          fill: #00918c !important;
        }
      }
    }
  }
  .dialog-footer {
    display: flex;
    justify-content: flex-end;
    .btn {
      margin-left: 20px;
      // width: 76px;
      // height: 32px;
      width: 96px;
      height: 42px;
      border-radius: 10px 10px 10px 10px;
      &:nth-child(1) {
        margin-left: 0;
      }
    }
  }

  .el-input__wrapper,
  .el-textarea__inner,
  .el-select__wrapper {
    border-radius: 5px !important;
    box-shadow: inset 1px 1px 6px 1px rgba(188, 198, 214, 0.14) !important;
    background-color: #f5f5f513 !important;
    border: 1px solid #edeff4 !important;
    // font-size: 18px !important;
    font-size: 16px !important;
    color: #3b4664 !important;
    &::placeholder,
    .is-transparent {
      font-size: 16px !important;
      color: #626c7187 !important;
    }
  }
  .el-form-item__label {
    font-weight: 400;
    font-size: 15px;
    color: #3b4664 !important;
  }
  .tips {
    font-weight: 400;
    font-size: 12px;
    color: #8f9bb2 !important;
  }

  .el-form-item {
    .el-date-editor {
      width: 100% !important;
      // font-size: 18px !important;
      font-size: 16px !important;
      color: #3b4664 !important;
    }
    .el-cascader {
      width: 100% !important;
    }
  }
}
.content,
.el-dialog {
  .el-input__wrapper,
  .el-textarea__inner,
  .el-select__wrapper {
    border-radius: 5px !important;
    box-shadow: inset 1px 1px 6px 1px rgba(188, 198, 214, 0.14) !important;
    background-color: #f5f5f513 !important;
    border: 1px solid #edeff4 !important;
    font-size: 16px !important;
    color: #3b4664 !important;
    font-family: inherit !important;
    font-weight: 400 !important;
    &::placeholder,
    .is-transparent {
      font-size: 16px !important;
      color: #716a6287 !important;
    }
  }
  .el-input__inner {
    font-size: 16px !important;
    color: #3b4664 !important;
    font-weight: 400;
    font-family: inherit !important;
    &::placeholder,
    .is-transparent {
      font-weight: 400 !important;
      font-size: 16px !important;
      color: #716a6287 !important;
    }
  }
}
.content {
  .cover-name {
    display: flex;
    align-items: center;
    justify-content: center;
    .cover-img {
      width: 60%;
      height: 106px;
    }
    span {
      width: 40%;
      display: inline-block;
    }
  }
  // .option-btn {
  //   display: flex !important;
  //   align-items: center !important;
  //   justify-content: flex-start !important;
  // }
}

// 改变el-tabs样式，下划线颜色
.el-tabs__active-bar {
  background: linear-gradient(
    90deg,
    #12af6c 0%,
    rgba(54, 185, 150, 0.02) 100%
  ) !important;
  height: 4px !important;
}

//el-table的自定义样式
.el-table {
  th,
  td {
    padding: 6px 0;
  }
  tr {
    //height: 7vh;
    border-radius: 10px;
    // background: #ffffff !important;
    color: #000000;
    // &.hover-row > td {
    //   background-color: #f5faf8 !important;
    // }
  }
  th {
    height: 68px;
    // background: linear-gradient(180deg, #cacedb 0%, #adb3c5 100%);
    background: #f5faf8 !important;
    //font-family: PingFang SC, PingFang SC;
    font-weight: 400;
    font-size: 15px;
    color: #3d4040;
    text-align: center;
    font-style: normal;
    text-transform: none;
  }
  th.is-leaf,
  td {
    border-bottom: 1px solid #c1c7d5 !important;
  }

  //鼠标点击active样式,highlight-current-row
  .el-table__body tr.current-row > td.el-table__cell {
    background: linear-gradient(180deg, #fafdfc 0%, #eafaf0 100%) !important;
    // box-shadow: 0px 3px 6px 1px rgba(25, 206, 185, 0.17) !important;
  }
  //鼠标悬停样式
  tbody tr:hover > td {
    background: linear-gradient(180deg, #fafdfc 0%, #eafaf0 100%) !important;
  }

  //操作的按钮样式
  .option-btn {
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .btn {
    width: 73px;
    height: 30px;
    margin-left: 20px;
    &:nth-child(1) {
      margin-left: 0;
    }
  }
}
// .el-table--enable-row-hover .el-table__body tr:hover > td {
//   background-color: #2d93ed2e !important;
//   color: #656565;
// }
.el-table--border {
  border-width: 0 !important;
  th,
  td {
    border-right: 1px solid #c1c7d5 !important;
  }
}

.el-scrollbar__bar {
  z-index: 15 !important;
}

// el-pagination
.el-pagination.is-background .el-pager li.is-active {
  background: #00918c !important;
}
.el-pagination {
  .el-select__wrapper {
    font-size: 14px !important;
    .is-transparent {
      font-size: 14px !important;
    }
  }
}

.checkbox-rectangle-green {
  .el-checkbox__input.is-checked .el-checkbox__inner::after {
    background: url("@/assets/images/active2.png") no-repeat !important;
    background-size: 100% 100% !important;
    border: none !important;
  }
  .el-checkbox.is-checked .el-checkbox__label {
    color: #00918c;
  }
}
.checkbox-rectangle-orange {
  .el-checkbox__input.is-checked .el-checkbox__inner::after {
    background: url("@/assets/images/active3.png") no-repeat !important;
    background-size: 100% 100% !important;
    border: none !important;
  }
  .el-checkbox.is-checked .el-checkbox__label {
    color: #e5601a !important;
  }
}
.checkbox-circle-green {
  .el-checkbox__input.is-checked .el-checkbox__inner::after {
    border: none !important;
    background: url("@/assets/images/radio-active2.png") no-repeat !important;
    background-size: 100% 100% !important;
  }
  .el-checkbox.is-checked .el-checkbox__label {
    color: #00918c;
  }
}
.checkbox-circle-green2 {
  .el-checkbox__input.is-checked .el-checkbox__inner::after {
    border: none !important;
    background: url("@/assets/images/radio-active3.png") no-repeat !important;
    background-size: 100% 100% !important;
  }
  .el-checkbox.is-checked .el-checkbox__label {
    color: #00918c;
  }
}

.checkbox-circle-orange {
  .el-checkbox__input.is-checked .el-checkbox__inner::after {
    border: none !important;
    background: url("@/assets/images/active.png") no-repeat !important;
    background-size: 100% 100% !important;
  }
  .el-checkbox.is-checked .el-checkbox__label {
    color: #e5601a !important;
  }
}

.el-checkbox__input.is-checked .el-checkbox__inner {
  background: transparent !important;
}
.el-checkbox__input.is-checked .el-checkbox__inner::after {
  opacity: 1 !important;
}

/* 自定义复选框样式 */
.custom-checkbox-circle {
  .el-checkbox {
    color: #3b4664 !important;
  }
  .el-checkbox__label {
    font-weight: 500 !important;
    font-size: 18px !important;
    // color: #3b4664 !important;
  }
  .el-checkbox__input.is-checked .el-checkbox__inner::after {
    content: "";
    border: none;
    background: url("@/assets/images/active.png") no-repeat;
    background-size: 100% 100%;
    width: 27px;
    height: 27px;
    left: 0;
    top: 0;
  }
  .el-checkbox__input.is-checked .el-checkbox__inner:after {
    transform: rotate(0deg) scaleY(1);
  }
  // .el-checkbox__input.is-checked + .el-checkbox__label {
  //   // margin-top: 1px;
  // }
  .el-checkbox__inner {
    background: url("@/assets/images/un-active.png") no-repeat;
    background-size: 100% 100%;
    border-radius: 50%;
    width: 27px;
    height: 27px;
  }
  .el-checkbox__input.is-focus .el-checkbox__inner,
  .el-checkbox__inner:hover {
    width: 27px;
    height: 27px;
    border: none;
    border-radius: 50%;
  }
  .el-checkbox__input.is-checked .el-checkbox__inner {
    border: none;
    background: transparent;
    border-radius: 50%;
  }
  .el-checkbox__inner:hover {
    border: none;
    border-radius: 50%;
  }

  //el-tree的样式
  .disabled-tree {
    pointer-events: none;
  }
  .el-tree-node__content {
    margin: 5px 0 5px 0;
  }
  .el-tree-node__label {
    //font-family: PingFang SC, PingFang SC;
    font-weight: 500 !important;
    font-size: 18px !important;
    color: #3b4664 !important;
  }
  .el-checkbox__input.is-indeterminate .el-checkbox__inner {
    background: linear-gradient(
      180deg,
      #e5601a 0%,
      rgba(237, 160, 119, 0.99) 100%
    ) !important;
    border: none !important;
    box-shadow: none !important;
  }
  .el-checkbox__input.is-indeterminate .el-checkbox__inner:before {
    top: 13px;
  }
}

/* 自定义select下拉箭头的样式 */
.el-select__suffix {
  i {
    width: 24px !important;
  }
  svg {
    color: #00918c !important;
    font-size: 18px !important;
  }
}
// .el-icon svg {
//   width: 100% !important;
//   height: 100% !important;
// }

//默认的自定义单选框
.el-radio__input.is-checked .el-radio__inner {
  background: transparent !important;
}
.el-radio.is-checked .el-radio__label {
  color: #e5601a !important;
}
.el-radio__input.is-checked .el-radio__inner::after {
  opacity: 1 !important;
} //避免图标初次渲染错位
.el-radio__input {
  width: 27px;
  height: 27px;
  .el-radio__inner {
    border: none !important;
    background: url("@/assets/images/un-active.png") no-repeat;
    background-size: 100% 100%;
    border-radius: 50%;
    width: 27px;
    height: 27px;
    &::after {
      border: none !important;
      background: url("@/assets/images/active.png") no-repeat;
      background-size: 100% 100%;
      border-radius: 50%;
      width: 27px;
      height: 27px;
      opacity: 0;
    }
  }
  .el-radio__label {
    font-weight: 500 !important;
    font-size: 18px !important;
    color: #3b4664 !important;
  }
}

//绿色的自定义单选框
.custom-el-radio {
  .el-radio__input {
    width: 27px;
    height: 27px;
  }
  .el-radio__inner {
    border: none !important;
    background: url("@/assets/images/radio-un-active2.png") no-repeat;
    background-size: 100% 100%;
    border-radius: 50%;
    width: 27px;
    height: 27px;
    &::after {
      border: none !important;
      background: url("@/assets/images/radio-active2.png") no-repeat;
      background-size: 100% 100%;
      border-radius: 50%;
      width: 27px;
      height: 27px;
      opacity: 0;
    }
  }

  .el-radio__input.is-checked .el-radio__inner {
    background: transparent !important;
  }
  .el-radio__input.is-checked .el-radio__inner::after {
    opacity: 1 !important;
  } //避免图标初次渲染错位
  .el-radio__label {
    font-weight: 400 !important;
    font-size: 15px !important;
    color: #8d9295 !important;
  }
  .el-radio__input.is-checked + .el-radio__label {
    color: #3b4664 !important;
  }
  .el-radio.is-checked .el-radio__label {
    color: #00918c !important;
  }
}

.pre-header,
.component-tab {
  width: 100%;
  .el-tabs__item {
    //font-family: PingFang SC, PingFang SC !important;
    font-weight: 400 !important;
    font-size: 22px !important;
    color: #7a7e7e !important;
    padding: 0 30px;
  }
  .el-tabs__item:nth-child(2) {
    padding: 0 30px 0 0 !important;
  }
  .el-tabs__nav-wrap::after {
    position: static !important;
  }
  // .el-tabs__active-bar {
  //   background-color: #16b06e !important;
  // }
  .el-tabs__item:hover,
  .el-tabs__item.is-active {
    font-weight: 600 !important;
    color: #3d4040 !important;
  }
}

.header-radio-group {
  .is-active {
    .el-radio-button__inner {
      background: linear-gradient(180deg, #33cec9 0%, #1c8d84 100%) !important;
      border-radius: 3px 3px 3px 3px !important;
    }
  }
  .el-radio-button__inner {
    background: linear-gradient(180deg, #cdddd6 0%, #becec4 100%) !important;
    border-radius: 3px 3px 3px 3px !important;
    border: 0px !important;
    //font-family: PingFang SC, PingFang SC !important;
    font-weight: 400 !important;
    font-size: 18px !important;
    box-shadow: none !important;
  }
  .el-radio-button {
    margin-left: 20px;
    &:nth-child(1) {
      margin-left: 0;
    }
    :hover {
      background: linear-gradient(180deg, #33cec9 0%, #1c8d84 100%) !important;
      border-radius: 3px 3px 3px 3px !important;
      color: #fff;
    }
  }
}

.custom-checkbox-rectangle,
.dialog-tree,
.dialog-checkbox2,
.el-table .cell {
  .el-checkbox {
    color: #3b4664 !important;
  }
  .el-checkbox__label {
    font-weight: 500 !important;
    font-size: 18px !important;
    // color: #3b4664 !important;
  }
  .el-checkbox__input.is-indeterminate .el-checkbox__inner {
    background: #1c8d84 !important;
    border: none !important;
    box-shadow: none !important;
  }

  .el-checkbox__input.is-indeterminate .el-checkbox__inner:before {
    top: 9px;
    box-shadow: none !important;
  }
  .el-tree-node__content {
    margin: 5px 0 5px 0;
  }
  .el-tree-node__label {
    //font-family: PingFang SC, PingFang SC;
    font-weight: 500 !important;
    font-size: 18px !important;
    color: #3b4664 !important;
  }
  .el-checkbox__input.is-checked .el-checkbox__inner::after {
    content: "";
    border: none;
    background: url("@/assets/images/active2.png") no-repeat;
    background-size: 100% 100%;
    width: 22px;
    height: 22px;
    left: 0;
    top: 0;
  }
  .el-checkbox__input.is-checked .el-checkbox__inner:after {
    transform: rotate(0deg) scaleY(1);
  }
  // .el-checkbox__input.is-checked + .el-checkbox__label {
  //   // margin-top: 1px;
  // }
  .el-checkbox__inner {
    background: url("@/assets/images/un-active2.png") no-repeat;
    background-size: 100% 100%;
    border-radius: 6px;
    width: 22px;
    height: 22px;
  }
  .el-checkbox__input.is-focus .el-checkbox__inner,
  .el-checkbox__inner:hover {
    width: 22px;
    height: 22px;
    border: none;
    border-radius: 6px;
  }
  .el-checkbox__input.is-checked .el-checkbox__inner {
    border: none;
    background: transparent;
    border-radius: 6px;
  }
  .el-checkbox__inner:hover {
    border: none;
    border-radius: 6px;
  }
}

.el-tag.el-tag--success {
  color: #00918c !important;
}
//遮罩层的自定义样式
//设置遮罩层颜色
// .el-loading-mask{
//   background-color: rgba(0,0,0,0.2);
// }

/* 自定义时间图标的样式 */
.el-range__icon,
.el-date-editor {
  svg {
    color: #00918c !important;
    font-size: 18px !important;
  }
  color: #00918c !important;
  font-size: 18px !important;
  .el-input__inner {
    font-size: 16px !important;
    color: #3b4664 !important;
  }
}
//设置图标颜色
.el-loading-spinner .path {
  stroke: #00918c !important;
}
//设置文字颜色
.el-loading-spinner .el-loading-text {
  color: #00918c !important;
}

//设置el-dropdown全局的自定义样式
.el-dropdown-menu__item:not(.is-disabled):focus {
  color: #00918c !important;
  background: linear-gradient(
    180deg,
    rgba(228, 250, 246, 0.56) 0%,
    #ffffff 100%
  ) !important;
  font-weight: 500;
  font-size: 16px;
}
.custom-el-switch {
  .el-switch.is-checked .el-switch__core {
    background: linear-gradient(180deg, #33cec9 0%, #1c8d84 100%) !important;
    box-shadow: inset 0px 2px 4px 1px rgba(67, 213, 190, 0.14) !important;
    border-color: transparent !important;
    font-weight: 500;
    font-size: 16px;
    color: #00918c;
  }
  .el-switch.is-checked .el-switch__core .el-switch__action {
    background: #fff !important;
  }
  .el-switch__core {
    background: rgb(199 215 206) !important;
    border: transparent !important;
    .el-switch__action {
      background: linear-gradient(180deg, #33cec9 0%, #1c8d84 100%) !important;
    }
  }
}

// 自定义el-tooltip背景框颜色
.custom-tooltip-popper {
  background: linear-gradient(180deg, #cdddd6 0%, #becec4 100%) !important;
  box-shadow: 0px 3px 6px 1px rgba(119, 147, 137, 0.16) !important;
  font-weight: 400 !important;
  font-size: 14px !important;
  color: #3b4664 !important;
  border: none !important;
  white-space: pre-line !important; // 保留换行
  max-width: 420px; // 可选，限制宽度自动换行
  word-break: break-all; // 可选，长词换行
}
//覆盖 el-tooltip 的箭头样式
.custom-tooltip-popper .el-popper__arrow {
  width: 0 !important;
  height: 0 !important;
  border-style: solid !important;
  border-width: 6px !important;
  border-color: transparent transparent #becec4 transparent !important;
  background: none !important;
  transform: translateY(-6px) !important;
}
.custom-tooltip-popper .el-popper__arrow::before {
  content: none !important; /* 移除伪元素 */
}

//自定义input输入框内嵌套append的样式，使其嵌套在框内
.input-with-inner-append {
  .el-input-group__append {
    background-color: transparent !important;
    box-shadow: none !important;
    border: none !important;
    color: #606266;
    padding: 0 10px;
    position: absolute;
    right: 0;
    top: 50%;
    transform: translateY(-50%);
    z-index: 1;
  }

  .el-input__wrapper {
    padding-right: 70px;
  }

  .el-input__clear {
    right: 30px;
  }
}
