{"name": "gkadmin", "version": "2.8.0", "private": true, "scripts": {"preinstall": "npx only-allow pnpm", "dev": "vite serve --mode development", "build:prod": "vite build --mode production && vue-tsc --noEmit", "build:stage": "vite build --mode staging && vue-tsc --noEmit", "prepare": "husky install", "lint:eslint": "eslint  --fix --ext .ts,.js,.vue ./src ", "lint:prettier": "prettier --write \"**/*.{js,cjs,ts,json,tsx,css,less,scss,vue,html,md}\"", "lint:stylelint": "stylelint  \"**/*.{css,scss,vue}\" --fix", "lint:lint-staged": "lint-staged", "commit": "git-cz"}, "config": {"commitizen": {"path": "node_modules/cz-git"}}, "lint-staged": {"*.{js,ts}": ["eslint --fix", "prettier --write"], "*.{cjs,json}": ["prettier --write"], "*.{vue,html}": ["eslint --fix", "prettier --write", "stylelint --fix"], "*.{scss,css}": ["stylelint --fix", "prettier --write"], "*.md": ["prettier --write"]}, "dependencies": {"@element-plus/icons-vue": "^2.3.1", "@vitejs/plugin-vue": "^4.6.0", "@vueuse/core": "^10.7.1", "@wangeditor/editor": "^5.1.23", "@wangeditor/editor-for-vue": "5.1.10", "axios": "^1.6.3", "echarts": "^5.4.3", "element-china-area-data": "5.0.2", "element-plus": "^2.4.4", "esdk-obs-browserjs": "3.24.3", "file-saver": "^2.0.5", "lodash-es": "^4.17.21", "mockjs": "^1.1.0", "net": "^1.0.2", "nprogress": "^0.2.0", "path-browserify": "^1.0.1", "path-to-regexp": "^6.2.1", "pinia": "^2.1.7", "pinia-plugin-persist": "^1.0.0", "screenfull": "^6.0.2", "sockjs-client": "1.6.1", "sortablejs": "^1.15.1", "stompjs": "^2.3.3", "terser": "^5.26.0", "vue": "^3.3.13", "vue-i18n": "9.2.2", "vue-qrcode": "^2.2.2", "vue-router": "^4.2.5", "vuedraggable": "^4.1.0", "xgplayer": "^3.0.20", "xgplayer-hls": "^3.0.20", "xlsx": "^0.18.5"}, "devDependencies": {"@commitlint/cli": "^17.8.1", "@commitlint/config-conventional": "^17.8.1", "@iconify-json/ep": "^1.1.14", "@types/file-saver": "^2.0.7", "@types/lodash": "^4.14.202", "@types/nprogress": "^0.2.3", "@types/path-browserify": "^1.0.2", "@types/postcss-pxtorem": "^6.0.3", "@types/sockjs-client": "^1.5.4", "@types/sortablejs": "^1.15.7", "@types/stompjs": "^2.3.9", "@typescript-eslint/eslint-plugin": "^5.62.0", "@typescript-eslint/parser": "^5.62.0", "@vitejs/plugin-vue-jsx": "^3.1.0", "amfe-flexible": "^2.2.1", "autoprefixer": "^10.4.20", "commitizen": "^4.3.0", "cz-git": "^1.8.0", "eslint": "^8.56.0", "eslint-config-prettier": "^8.10.0", "eslint-plugin-import": "^2.29.1", "eslint-plugin-prettier": "^4.2.1", "eslint-plugin-vue": "^9.19.2", "fast-glob": "^3.3.2", "husky": "^8.0.3", "lint-staged": "^13.3.0", "postcss": "^8.4.43", "postcss-html": "^1.5.0", "postcss-preset-env": "^10.0.2", "postcss-pxtorem": "^6.1.0", "postcss-scss": "^4.0.9", "prettier": "^2.8.8", "sass": "^1.69.5", "stylelint": "^15.11.0", "stylelint-config-html": "^1.1.0", "stylelint-config-recess-order": "^4.4.0", "stylelint-config-recommended-scss": "^13.1.0", "stylelint-config-recommended-vue": "^1.5.0", "stylelint-config-standard": "^34.0.0", "stylelint-config-standard-scss": "^11.1.0", "typescript": "^5.3.3", "unocss": "^0.58.1", "unplugin-auto-import": "^0.15.3", "unplugin-icons": "^0.16.6", "unplugin-vue-components": "^0.24.1", "vite": "^5.0.10", "vite-plugin-mock": "^3.0.0", "vite-plugin-svg-icons": "^2.0.1", "vue-tsc": "^1.8.27"}, "repository": "https://gitee.com/youlaiorg/vue3-element-admin.git", "author": "gknowledge.cn ", "license": "MIT", "engines": {"node": ">=18.0.0"}}