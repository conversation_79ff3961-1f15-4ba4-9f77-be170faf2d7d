<template>
  <div class="exam-action-container">
    <div class="container-header">
      <div class="left">
        <span>题库--{{ questionBankForm.name }}详情 </span>
      </div>
      <div class="right">
        <div class="btn primary-btn" @click="handleBack">返回</div>
      </div>
    </div>
    <div class="content">
      <el-scrollbar
        style="width: 100%; height: 100%"
        warp-style="overflow-x: hidden;"
        class="exam-scrollbar"
      >
        <div class="paper-info">
          <ExamPaper
            :isOperable="false"
            :showAnalysis="true"
            :showAnswerType="'all'"
            :showSelectedAnswer="false"
            :showAllAnswer="true"
            :showQuestionScore="false"
            :paperData="questionBankForm.questions"
          />
        </div>
      </el-scrollbar>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useUserStore } from "@/store/modules/user";
import { useRoute, useRouter } from "vue-router";
import {
  parseTime,
  resetReactiveObject,
  secondsToHoursAndMinutes,
  numberToChinese,
  numberToChinese2,
} from "@/utils";
import { checkUserPermission } from "@/utils/auth";
import ExamPaper from "./components/ExamPaper.vue";
import { getQuestionBankDetail } from "@/api/exam";

const { proxy } = getCurrentInstance() as any;
// proxy 替代 this
defineOptions({
  name: "ExamAction",
  inheritAttrs: false,
});
const userStore = useUserStore();
const route = useRoute();
const router = useRouter();

const type: any = route.query.type;
const questionBankId: any = route.query.id;
const typeMap = reactive<any>({
  create: { value: "create", label: "新增" },
  edit: { value: "edit", label: "修改" },
  detail: { value: "detail", label: "详情" },
});

const questionBankForm = reactive<any>({
  name: "",
  enabled: "",
  topic_ids: "",
  questions: [],
});
const questionData = reactive<any>({
  name: "", //题目标题
  image_urls: "", //图片的路径列表，如：["/image/platform/xxx.jpg", "/image/platform/xxx.jpg"]
  remark: "", //题目解析
  q_type: "",
  content: "", // 当题目类型为选择题（p_type=10和20）时，为json格式的选项内容，如：[{"A": "xxx"}, {"B": "xxx"}]
  answers: "", // 答案，有多个时用逗号隔开，如："A, B"
  seq: "",
});

onBeforeMount(() => {});
onMounted(() => {
  if (route.query.id || route.params.id) {
    getQBDetail(route.query.id || route.params.id);
  }
});

async function getQBDetail(id: any) {
  try {
    const response = await getQuestionBankDetail(id);
    const data = response.data.q_bank;
    Object.assign(questionBankForm, data);
    questionBankForm.topic_ids = data.gk_topics.toString();
    questionBankForm.questions = data.questions.map((item: any) => {
      let question: any = {
        id: item.id,
        name: item.name,
        title: item.name,
        image_urls: item.image_urls || item.images,
        remark: item.remark,
        q_type: item.q_type || item.category,
        content: item.content,
        answers: item.answers,
        correctAnswer: item.answers,
        seq: item.seq,
      };
      // if (question.q_type == 40) {
      //   console.log("添加判断题默认选项");
      //   question.content = JSON.stringify([{ A: "正确" }, { B: "错误" }]);
      // }
      return question;
    });
  } catch (error) {
    console.error("err:", error);
  }
}

function handleBack() {
  router.go(-1);
}
</script>

<style scoped lang="scss">
.exam-action-container {
  height: 95%;
  margin: 20px;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 3px 20px 1px rgb(109 125 126 / 7%);

  .container-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 20px;

    .left {
      display: flex;
      align-items: center;
      justify-content: space-between;
      font-size: 20px;
      font-weight: 500;
      color: #3b4664;

      .tips {
        margin-left: 30px;
        font-size: 14px;
        font-weight: 400;
        color: #f23c33;
      }
    }

    .right {
      .btn {
        width: 52px;
        height: 28px;
        font-size: 13px;
        font-weight: 400;
        border-radius: 13px;
      }
    }
  }

  .content {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: calc(100% - 70px);
    padding: 10px 0;

    .exam-scrollbar,
    .el-scrollbar__wrap {
      width: 100%;
    }

    :deep(.el-scrollbar__view) {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 100%;
    }

    .paper-info {
      width: 95%;
      border: 1px solid #fff;
      border-radius: 0;
      box-shadow: 0 0 6px 1px rgb(191 226 206 / 16%),
        inset 0 0 10px 1px rgb(19 69 65 / 20%);
    }
  }
}
</style>
