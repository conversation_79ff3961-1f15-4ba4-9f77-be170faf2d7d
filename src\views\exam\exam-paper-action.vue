<template>
  <div class="exam-paper-action-container">
    <div class="container-header">
      <div class="left">
        <span>
          试卷-- {{ type == "edit" ? examPaperForm?.name : "" }}
          {{ typeMap[type].label }}
        </span>
      </div>
      <div class="right">
        <div class="btn primary-btn" @click="handleBack">返回</div>
      </div>
    </div>
    <div class="content">
      <div class="left-content">
        <el-scrollbar
          style="width: 100%; height: 100%"
          warp-style="overflow-x: hidden;"
          class="exam-paper-scrollbar"
        >
          <div class="exam-paper-form">
            <div class="block">
              <div class="label-title">基础信息</div>

              <div class="input-item">
                <div class="label"><span>* </span>试卷名称</div>
                <div class="input">
                  <el-input
                    v-model="examPaperForm.name"
                    placeholder="请输入"
                    clearable
                    size="large"
                    class="input-with-inner-append"
                  />
                </div>
              </div>
              <div class="input-item">
                <div class="label"><span>* </span>试卷限时</div>
                <div class="input">
                  <el-input
                    v-model="examPaperForm.time_limit"
                    placeholder="请输入"
                    clearable
                    size="large"
                    class="input-with-inner-append"
                  >
                    <template #append>分钟</template>
                  </el-input>
                </div>
              </div>
              <div class="input-item">
                <div class="label"><span>* </span>试卷说明</div>
                <div class="input">
                  <el-input
                    v-model="examPaperForm.remark"
                    placeholder="请输入"
                    clearable
                    size="large"
                    class="input-with-inner-append"
                  />
                </div>
              </div>
              <div class="input-item">
                <div class="label"><span>* </span>关联题库</div>
                <div class="input">
                  <el-select
                    v-model="examPaperForm.q_bank_id"
                    placeholder="请选择题库"
                    size="large"
                    @change="handleQbChange"
                  >
                    <el-option
                      v-for="item in questionBankOptions"
                      :key="item.value"
                      :label="item.name"
                      :value="item.id"
                    />
                  </el-select>
                </div>
              </div>
            </div>
            <div class="block">
              <div class="label-title">已选题目</div>
              <div class="label-tips">从题库中选择试卷试题。</div>
              <div class="input-item">
                <div class="label">已选</div>
                <div class="input">
                  <el-input
                    v-model="examPaperForm.count"
                    placeholder="请选择题目"
                    clearable
                    size="large"
                    class="input-with-inner-append"
                    readonly
                  >
                    <template #append>题</template>
                  </el-input>
                </div>
              </div>
            </div>
            <div class="block">
              <div class="label-title">
                题目分值
                <span class="label-tips2"
                  >注：各题型分数默认值均为0，请注意修改！</span
                >
              </div>

              <div
                class="input-item"
                v-for="(item, index) in questionOptions"
                :key="index"
              >
                <div class="label">
                  {{ item.label }}：已选{{ item.count }} 题，每题分值
                </div>
                <div class="input">
                  <el-input
                    v-model="item.score"
                    placeholder="请输入"
                    oninput="value=value.replace(/[^\d]/g,'')"
                    clearable
                    size="large"
                    class="input-with-inner-append"
                    @change="handleScoreChange"
                  >
                    <template #append>分</template>
                  </el-input>
                </div>
              </div>
              <div class="input-item">
                <div class="label">满分：</div>
                <div class="input">
                  <el-input
                    v-model="examPaperForm.total_scores"
                    clearable
                    size="large"
                    class="input-with-inner-append"
                    readonly
                  >
                    <template #append>分</template>
                  </el-input>
                </div>
              </div>
            </div>
          </div>
        </el-scrollbar>
        <div class="footer">
          <div class="left">
            <div class="text-btn" @click="handleQuestions">
              <i-ep-circle-plus style="margin-right: 5px" />
              添加题目
            </div>
          </div>
          <div class="right">
            <div class="btn cancel-btn" @click="handleBack">取 消</div>
            <div class="btn primary-btn" @click="handleSubmit">保 存</div>
          </div>
        </div>
      </div>
      <div class="right-content">
        <el-scrollbar
          style="width: 100%; height: calc(100% - 1rem)"
          warp-style="overflow-x: hidden;"
          class="exam-paper-scrollbar"
        >
          <div class="exam-paper-container">
            <div v-if="examPaperForm.questions.length == 0" class="empty-state">
              <div class="empty-icon">📝</div>
              <div class="empty-text">暂无题目</div>
              <div class="empty-desc">请在左侧选择题目</div>
            </div>

            <div v-else class="selected-questions">
              <draggable
                v-model="examPaperForm.questions"
                class="question-drag-container"
                handle=".drag-sort"
                :animation="200"
                item-key="id"
                ghost-class="ghost-item"
                chosen-class="chosen-item"
                drag-class="drag-item"
                @end="onDragEnd"
              >
                <template #item="{ element, index }">
                  <div class="question-item-wrapper">
                    <component
                      :is="questionMap[element.q_type].component"
                      :key="index"
                      :questionIndex="index"
                      :questionData="element"
                      :isOperable="false"
                      :showAllAnswer="true"
                      :showSelectedAnswer="false"
                      :showGetScore="false"
                      :showQuestionScore="false"
                      :showCheckBox="false"
                      :itemStyle="'shadowBorder'"
                      :showAnswerTime="false"
                      :showAnalysis="false"
                      :showDrag="true"
                      :showDelete="true"
                      :showAnswerType="'all'"
                      @handle-action="handleDelete"
                    />
                  </div>
                </template>
              </draggable>
            </div>
          </div>
        </el-scrollbar>
      </div>
    </div>

    <!-- 题目选择弹窗 -->
    <el-dialog
      class="questionBankDialog"
      v-model="questionDialog.visible"
      :title="questionDialog.title"
      :width="questionDialog.width"
      append-to-body
      @close="closeQuestionDialog"
    >
      <template #header>
        <div class="dialog-title">
          {{ questionDialog.title }}
        </div>
      </template>
      <div class="dialog-body">
        <div class="dialog-content-header">
          <!-- <div class="filter-row">
            <el-select
              v-model="questionQueryParams.topic_id"
              placeholder="题库"
              clearable
              size="large"
              @change="handleQuestionQuery"
            >
              <el-option
                v-for="item in questionBankOptions"
                :key="item.value"
                :label="item.name"
                :value="item.id"
              />
            </el-select>
          </div> -->
          <div class="filter-row">
            <el-select
              v-model="questionQueryParams.qtype"
              placeholder="题型"
              clearable
              size="large"
              @change="handleQuestionQuery"
            >
              <el-option
                v-for="item in questionOptions"
                :key="item.value"
                :label="item.label"
                :value="item.q_type"
              />
            </el-select>
          </div>
          <div class="filter-row">
            <el-input
              v-model="questionQueryParams.search"
              placeholder="关键字"
              clearable
              size="large"
            />
          </div>
          <div class="btn primary-btn" @click="handleQuestionQuery">
            <i-ep-search /> 搜索
          </div>
        </div>

        <div class="selection-controls">
          <div
            class="left-controls custom-checkbox-rectangle checkbox-rectangle-green"
          >
            <el-checkbox
              v-model="selectAll"
              :indeterminate="isIndeterminate"
              @change="handleSelectAll"
            >
              全选
            </el-checkbox>
            <div
              class="btn text-btn"
              @click="handleSelectNone"
              :disabled="selectedQuestions.length == 0"
            >
              全不选
            </div>
          </div>
        </div>

        <div
          class="dialog-content-table"
          v-loading="questionLoading"
          element-loading-text="Loading"
          element-loading-background="#ffffffb4"
        >
          <el-scrollbar
            style="width: 100%; height: 100%"
            warp-style="overflow-x: hidden;"
          >
            <div class="question-list">
              <!-- 使用已有的题目组件来渲染题目内容 -->
              <ExamPaper
                :paperData="questionTableList"
                :selectedQuestionIds="selectedQuestionIds"
                :selectAllTrigger="selectAllTrigger"
                :selectNoneTrigger="selectNoneTrigger"
                :isOperable="false"
                :showAllAnswer="true"
                :showSelectedAnswer="false"
                :showGetScore="false"
                :showQuestionScore="false"
                :showCheckBox="true"
                :itemStyle="'shadowBorder'"
                :showAnswerTime="false"
                :showAnalysis="false"
                :showAnswerType="'all'"
                :showDelete="true"
                @handle-action="handleQuestionAction"
              />
            </div>
          </el-scrollbar>
        </div>

        <div class="dialog-content-footer">
          <pagination
            v-if="questionTotal > 0"
            v-model:total="questionTotal"
            v-model:page="questionQueryParams.pageNum"
            v-model:limit="questionQueryParams.pageSize"
            @pagination="getQuestionData"
          />
        </div>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <div class="left">
            <span>已选{{ selectedQuestions?.length || 0 }}道题目</span>
          </div>
          <div class="right">
            <div class="btn cancel-btn" @click="closeQuestionDialog">取 消</div>
            <div class="btn primary-btn" @click="handleSubmitQuestions">
              确 定
            </div>
          </div>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { useRoute, useRouter } from "vue-router";
import ExamPaper from "./components/ExamPaper.vue";
import FillBlank from "./components/FillBlank.vue";
import Judgement from "./components/Judgement.vue";
import MultipleChoice from "./components/MultipleChoice.vue";
import SingleChoice from "./components/SingleChoice.vue";
import draggable from "vuedraggable";
import {
  getExamPaperDetail,
  addExamPaper,
  updateExamPaper,
  getQuestionBanks,
  getQuestions,
  deleteQuestion,
} from "@/api/exam";
import { markRaw } from "vue";

defineOptions({
  name: "ExamPaperAction",
  inheritAttrs: false,
});

const route = useRoute();
const router = useRouter();

const type: any = route.query.type;
const examPaperId: any = route.query.id;
const typeMap = reactive<any>({
  create: { value: "create", label: "新增" },
  edit: { value: "edit", label: "修改" },
  detail: { value: "detail", label: "详情" },
});
const examPaperDetail = reactive<any>({});
const examPaperForm = reactive<any>({
  // id	:'',//Integer	记录id
  name: "", //String	试卷名称
  remark: "", //String	试卷说明
  time_limit: "", //	Integer	试卷限时，单位：分钟
  scores: "", //	Integer	总分
  questions: [], //Array	试题列表
  q_bank_id: "", //题库关联id
});
const questionData = reactive<any>({
  id: "", //	Integer	试题id
  name: "", //	String	试题题目
  scores: "", //	Integer	得分
  q_type: "", //Integer	试题类型，10-单选，20-多选，30-填空，40-判断
  content: "", //	String	题目内容，当题目类型为选择题（p_type=10和20）时，为json格式的选项内容，如：[{"A": "xxx"}, {"B": "xxx"}]
  answers: "", //String	题目的答案，有多个时用逗号隔开，如："A, B"
  remark: "", //String	提示解析
});
const questionBankOptions = ref<any>([]);
const questionOptions = ref<any>([
  { label: "单选题", q_type: 10, count: 0, score: 0 },
  { label: "多选题", q_type: 20, count: 0, score: 0 },
  { label: "填空题", q_type: 30, count: 0, score: 0 },
  { label: "判断题", q_type: 40, count: 0, score: 0 },
]);
const questionMap = reactive<any>({
  10: { component: markRaw(SingleChoice), label: "单选题" },
  20: { component: markRaw(MultipleChoice), label: "多选题" },
  30: { component: markRaw(FillBlank), label: "填空题" },
  40: { component: markRaw(Judgement), label: "判断题" },
});
const questionDialog = reactive<any>({
  visible: false,
  width: "50%",
  title: "选择题目",
});

const questionQueryParams = reactive<any>({
  search: "",
  topic_id: "",
  pageNum: 1,
  pageSize: 10,
});

const questionTableList = ref<any>([]);
const questionTotal = ref(0);
const questionLoading = ref(false);
const selectedQuestions = ref<any>([]);
const selectAll = ref(false);

// 用于触发ExamPaper组件的全选/全不选
const selectAllTrigger = ref(false);
const selectNoneTrigger = ref(false);

// 选中的题目ID列表，用于传递给ExamPaper组件
const selectedQuestionIds = computed(() => {
  return selectedQuestions.value.map((q: any) => q.id);
});

const isIndeterminate = computed(() => {
  const selectedCount = selectedQuestions.value.length;
  const totalCount = questionTableList.value.length;
  return selectedCount > 0 && selectedCount < totalCount;
});

// 监听选中题目变化，更新状态
watch(
  () => selectedQuestions.value,
  () => {
    updateSelectAllState();
  },
  { deep: true }
);

// 监听试卷题目变化，更新统计
watch(
  () => examPaperForm.questions,
  () => {
    updateQuestionStats();
    updatePaperTotalScore();
  },
  { deep: true }
);

onBeforeMount(() => {
  // 初始化题目统计
  updateQuestionStats();
});

onMounted(() => {
  getPaperData();
  getQuestionBankData();
});

function getQuestionBankData() {
  const params = {
    per_page: 9999,
    page: 1,
  };
  getQuestionBanks(params).then((res: any) => {
    questionBankOptions.value = res.data.q_banks.map((item: any) => {
      return item;
    });
  });
}
//关联题库变更，题目清空
function handleQbChange() {
  examPaperForm.questions = [];
  updateQuestionStats();
  updatePaperTotalScore();
}

function getPaperData() {
  if (examPaperId) {
    getExamPaperDetail(examPaperId).then((res: any) => {
      Object.assign(examPaperForm, res.data.exam_paper);
      examPaperForm.time_limit = res.data.exam_paper.time_limit;
      const dataSource = res.data.exam_paper.questions.map((item: any) => {
        item.q_type = item.category;
        return item;
      });
      const normalizedData = dataSource.map((item: any) => {
        if (item.q_type == 40) {
          item.content = JSON.stringify([{ A: "正确" }, { B: "错误" }]);
        }
        return normalizeQuestionData(item);
      });
      examPaperForm.questions = normalizedData;
      res.data.exam_paper.questions.forEach((item: any) => {
        questionOptions.value.forEach((option: any) => {
          if (option.q_type == item.category) {
            option.count++;
            option.score = item?.scores || 0;
          }
        });
      });

      handleScoreChange();
    });
  }
}

//题目组件内的数据格式
function normalizeQuestionData(questionData: any) {
  let normalized: any = {
    id: questionData.id,
    q_type: questionData.q_type || questionData.category,
    name: questionData.name,
    title: questionData.name || questionData.title,
    images: questionData.image_urls || questionData.images,
    score: questionData.scores || questionData.score || "",
    answers: questionData.answers || "",
    correctAnswer: questionData.answers,
    userAnswer: questionData.userAnswer || questionData.user_answers || "",
    options: parseQuestionOptions(questionData),
    remark: questionData.remark || "",
    content: questionData.content || "",
    seq: questionData.seq || "",
  };

  if (typeof normalized.images == "string") {
    normalized.images = JSON.parse(normalized.images);
  }

  return normalized;
}
// 解析题目选项
function parseQuestionOptions(questionData: any) {
  if (!questionData.content) return [];
  try {
    let content: any = [];
    // 如果content是字符串，尝试解析JSON
    if (typeof questionData.content === "string") {
      content = JSON.parse(questionData.content) || [];
    }
    // 如果content已经是数组，直接返回
    if (Array.isArray(questionData.content)) {
      content = questionData.content;
    }

    // let imgs: any = null;
    // if (typeof questionData.images == "string") {
    //   imgs = JSON.parse(questionData.images);
    // } else {
    //   imgs = questionData.images;
    // }

    // if (imgs && imgs.length) {
    //   content.forEach((item: any, index: any) => {
    //     item.img = imgs[index];
    //   });
    // }//不需要这个，content图片本身保存在选项中
    return content;
  } catch (error) {
    console.warn("Failed to parse question options:", error);
    return [];
  }
}

//题目选择弹窗方法
async function getQuestionData() {
  questionLoading.value = true;
  try {
    const params = {
      search: questionQueryParams.search || undefined,
      qbid: examPaperForm.q_bank_id,
      page: questionQueryParams.pageNum,
      per_page: questionQueryParams.pageSize,
      qtype: questionQueryParams.qtype || undefined,
    };
    const response: any = await getQuestions(params);

    const dataSource = response.data.questions.map((item: any) => {
      item.q_type = item.category;
      return item;
    });
    const normalizedData = dataSource.map((item: any) => {
      if (item.q_type == 40) {
        item.content = JSON.stringify([{ A: "正确" }, { B: "错误" }]);
      }
      return normalizeQuestionData(item);
    });
    questionTableList.value = normalizedData;
    questionTotal.value = response.total;
    questionLoading.value = false;
  } catch (error) {
    console.error("err:", error);
    questionLoading.value = false;
  }
}
function handleQuestions() {
  if (!examPaperForm.q_bank_id) {
    ElMessage.warning("请先选择题库");
    return;
  }
  questionDialog.visible = true;
  const q_bank_name = questionBankOptions.value.find(
    (item: any) => item.id == examPaperForm.q_bank_id
  )?.name;
  questionDialog.title = "题库-" + q_bank_name + "：选择题目";
  getQuestionData();
}

function closeQuestionDialog() {
  questionDialog.visible = false;
  selectedQuestions.value = [];
  selectAll.value = false;
  questionQueryParams.search = "";
  questionQueryParams.topic_id = "";
  questionQueryParams.qtype = "";
  questionQueryParams.pageNum = 1;
}

function handleQuestionQuery() {
  questionQueryParams.pageNum = 1;
  getQuestionData();
}

function isQuestionSelected(questionId: any) {
  return selectedQuestions.value.some((q: any) => q.id == questionId);
}

function handleQuestionSelect(question?: any, checked?: any) {
  if (checked) {
    if (!isQuestionSelected(question.id)) {
      selectedQuestions.value.push(question);
    }
  } else {
    const index = selectedQuestions.value.findIndex(
      (q: any) => q.id == question.id
    );
    if (index > -1) {
      selectedQuestions.value.splice(index, 1);
    }
  }
  updateSelectAllState();
}

function handleSelectAll(checked?: any) {
  if (checked) {
    // 全选
    selectedQuestions.value = [...questionTableList.value];
    // 触发ExamPaper组件的全选
    selectAllTrigger.value = !selectAllTrigger.value;
  } else {
    // 取消全选
    selectedQuestions.value = [];
    // 触发ExamPaper组件的全不选
    selectNoneTrigger.value = !selectNoneTrigger.value;
  }
  updateSelectAllState();
}

function handleSelectNone() {
  // 全不选
  selectedQuestions.value = [];
  selectAll.value = false;
  // 触发ExamPaper组件的全不选
  selectNoneTrigger.value = !selectNoneTrigger.value;
  updateSelectAllState();
}

function updateSelectAllState() {
  const selectedCount = selectedQuestions.value.length;
  const totalCount = questionTableList.value.length;
  selectAll.value = selectedCount == totalCount && totalCount > 0;
}

function formatQuestionsForPaper(questions: any[]) {
  // 将题目数据格式化为ExamPaper组件需要的格式
  return questions.map((question: any) => question);
}

function formatOptionsForPaper(question: any) {
  if (question.q_type == 10 || question.q_type == 20) {
    // 单选题或多选题
    if (question.options && Array.isArray(question.options)) {
      return question.options.map((option: any, index: number) => {
        const key = getOptionLabel(index);
        return { [key]: option.text || option };
      });
    }
  } else if (question.q_type == 40) {
    // 判断题
    return [{ A: "正确" }, { B: "错误" }];
  }
  return [];
}

function getOptionLabel(index: number) {
  return String.fromCharCode(65 + index);
}

function handleQuestionAction(payload: any) {
  // 处理从ExamPaper组件传来的题目选择事件
  if (payload.type == "select-question") {
    // 处理选择题目
    const question =
      payload.chosenQuestions[payload.chosenQuestions.length - 1];
    if (question) {
      const questionData = question;
      const questionId = questionData.id;

      if (!isQuestionSelected(questionId)) {
        selectedQuestions.value.push({
          id: questionId,
          title: questionData.name || questionData.title,
          name: questionData.name || questionData.title,
          q_type: questionData.q_type,
          images: questionData.image_urls || questionData.images,
          content: questionData.content,
          score:
            questionData.scores ||
            questionData.score ||
            getDefaultScore(questionData.q_type),
          options: questionData.options || [],
          correct_answer: questionData.answers || questionData.correct_answer,
          answers: questionData.answers || questionData.correct_answer,
          remark: questionData.remark || questionData.remark || "",
        });
      }
    }
  } else if (payload.type == "remove-question") {
    // 处理取消选择题目
    const question = payload.chosenQuestion;
    if (question) {
      const questionData = question;
      const questionId = questionData.id;

      const index = selectedQuestions.value.findIndex(
        (q: any) => q.id == questionId
      );
      if (index > -1) {
        selectedQuestions.value.splice(index, 1);
      }
    }
  } else if (payload.type == "select-all") {
    // 处理全选
    selectedQuestions.value = payload.chosenQuestions.map((question: any) => {
      const questionData = question;
      return {
        id: questionData.id,
        title: questionData.name || questionData.title,
        name: questionData.name || questionData.title,
        q_type: questionData.q_type,
        content: questionData.content,
        score:
          questionData.scores ||
          questionData.score ||
          getDefaultScore(questionData.q_type),
        options: questionData.options || [],
        correct_answer: questionData.answers || questionData.correct_answer,
        answers: questionData.answers || questionData.correct_answer,
        remark: questionData.remark || questionData.remark || "",
      };
    });
    selectAll.value = true;
  } else if (payload.type == "select-none") {
    // 处理全不选
    selectedQuestions.value = [];
    selectAll.value = false;
  }
  // 处理删除
  if (payload.type == "delete-question") {
    ElMessageBox.confirm("是否确认操作?", "提示", {
      confirmButtonText: "是",
      cancelButtonText: "否",
      type: "warning",
    }).then(() => {
      const questionId = payload.question.id;
      deleteQuestion(questionId).then(() => {
        ElMessage.success("删除成功");
        getQuestionData();
      });
    });
  }
  updateSelectAllState();
}

function handleSubmitQuestions() {
  if (selectedQuestions.value.length == 0) {
    ElMessage.warning("请至少选择一道题目");
    return;
  }

  let addedCount = 0;

  // 避免重复添加题目
  selectedQuestions.value.forEach((question: any) => {
    const existingIndex = examPaperForm.questions.findIndex(
      (q: any) => q.id == question.id
    );

    if (existingIndex == -1) {
      // 格式化题目数据

      const formattedQuestion = Object.assign(
        {},
        {
          id: question.id,
          name: question.title || question.name,
          title: question.title || question.name,
          q_type: question.q_type,
          content: question.content || "",
          options: question.options || formatQuestionContent(question),
          answers: question.correct_answer || question.answers,
          remark: question.remark || "",
          scores: question.scores || getDefaultScore(question.q_type),
          seq: examPaperForm.questions.length + addedCount + 1,
        }
      );

      examPaperForm.questions.push(formattedQuestion);
      addedCount++;
    }
  });

  // 更新题目统计和总分
  updateQuestionStats();
  updatePaperTotalScore();

  if (addedCount > 0) {
    ElMessage.success(`成功添加${addedCount}道题目`);
  } else {
    ElMessage.info("所选题目已存在于试卷中");
  }

  closeQuestionDialog();
}

function handleScoreChange() {
  // 更新题目总分
  updatePaperTotalScore();
}

function updatePaperTotalScore() {
  // 计算试卷总分
  // const totalScore = examPaperForm.questions.reduce(
  //   (sum: number, question: any) => {
  //     return sum + (question.scores || question.score || 5);
  //   },
  //   0
  // );
  //计算填入的分数计算总分
  const totalScore = questionOptions.value.reduce(
    (sum: number, option: any) => {
      // 每种题型的数量 * 分值 累加
      return sum + option.count * option.score;
    },
    0
  );
  examPaperForm.total_scores = totalScore * 1;
  examPaperForm.scores = totalScore * 1;
  // console.log("questionOptions.value", questionOptions.value, totalScore);
}

// 格式化题目
function formatQuestionContent(question: any) {
  if (question.q_type == 10 || question.q_type == 20) {
    // 单选题或多选题
    if (question.options && Array.isArray(question.options)) {
      return JSON.stringify(
        question.options.map((option: any, index: number) => {
          const key = String.fromCharCode(65 + index); // A, B, C, D...
          return { [key]: option.text || option };
        })
      );
    }
  } else if (question.q_type == 40) {
    // 判断题
    return JSON.stringify([{ A: "正确" }, { B: "错误" }]);
  }
  return question.content || "";
}

// 获取默认分值
function getDefaultScore(qType: number) {
  const scoreMap: any = {
    10: 0, // 单选题
    20: 0, // 多选题
    30: 0, // 填空题
    40: 0, // 判断题
  };
  questionOptions.value.forEach((option: any) => {
    if (option.q_type == qType) {
      scoreMap[qType] = option.score;
    }
  });

  return scoreMap[qType];
}

// 更新题目统计
function updateQuestionStats() {
  // 统计各类型题目数量
  const stats = examPaperForm.questions.reduce((acc: any, question: any) => {
    const type = question.q_type;
    acc[type] = (acc[type] || 0) + 1;
    return acc;
  }, {});

  // 更新questionOptions中的count
  questionOptions.value.forEach((option: any) => {
    option.count = stats[option.q_type] || 0;
  });

  // 更新总题目数
  examPaperForm.count = examPaperForm.questions.length;
}

//右侧试卷预览对应操作
function onDragEnd() {
  examPaperForm.questions.forEach((question: any, index: number) => {
    question.seq = index + 1;
  });

  updatePaperTotalScore();
}

function handleDelete(payload: any) {
  if (payload.type == "delete-question") {
    const questionId = payload.question.id;
    const index = examPaperForm.questions.findIndex(
      (q: any) => q.id == questionId
    );

    if (index > -1) {
      examPaperForm.questions.splice(index, 1);
      updateQuestionStats();
      updatePaperTotalScore();
    }
  }
}

function handleSubmit() {
  if (!examPaperForm.name) {
    ElMessage.warning("请输入试卷名称");
    return;
  }
  if (!examPaperForm.q_bank_id) {
    ElMessage.warning("请选择题库");
    return;
  }
  if (!examPaperForm.time_limit) {
    ElMessage.warning("请输入试卷限时");
    return;
  }
  if (!examPaperForm.questions.length) {
    ElMessage.warning("请添加试卷题目");
    return;
  }
  if (!examPaperForm.remark) {
    ElMessage.warning("请输入试卷说明");
    return;
  }

  const data: any = {
    name: examPaperForm.name,
    remark: examPaperForm.remark,
    time_limit: examPaperForm.time_limit,
    q_bank_id: examPaperForm.q_bank_id,
    enabled: true,
  };
  data.questions = examPaperForm.questions.map((item: any, index: any) => {
    return {
      id: item.id,
      seq: index,
      scores: getDefaultScore(item.q_type),
    };
  });

  data.total_scores = examPaperForm.total_scores;
  ElMessageBox.confirm("是否确认操作?", "提示", {
    confirmButtonText: "是",
    cancelButtonText: "否",
    type: "warning",
  }).then(() => {
    if (type == "create") {
      addExamPaper(data).then((res: any) => {
        if (res.status == 200) {
          ElMessage.success({
            message: "新增成功!",
          });
          handleBack();
        }
      });
    } else if (type == "edit") {
      updateExamPaper(examPaperId, data).then((res: any) => {
        if (res.status == 200) {
          ElMessage.success({
            message: "修改成功!",
          });
          handleBack();
        }
      });
    }
  });
}
function handleBack() {
  router.go(-1);
}
</script>

<style scoped lang="scss">
.exam-paper-action-container {
  height: 95%;
  margin: 20px;

  .container-header {
    display: flex;
    justify-content: space-between;
    padding: 20px;
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 3px 20px 1px rgb(109 125 126 / 7%);

    .left {
      display: flex;
      align-items: center;
      justify-content: space-between;
      font-size: 20px;
      font-weight: 500;
      color: #3b4664;

      .tips {
        margin-left: 30px;
        font-size: 14px;
        font-weight: 400;
        color: #f23c33;
      }
    }

    .right {
      .btn {
        width: 52px;
        height: 28px;
        font-size: 13px;
        font-weight: 400;
        border-radius: 13px;
      }
    }
  }

  .content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    height: calc(100% - 70px);
    padding: 10px 0;

    .left-content {
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      width: 487px;
      height: 100%;
      padding: 33px 25px;
      background: #fff;
      border-radius: 8px;
      box-shadow: 0 3px 20px 1px rgb(109 125 126 / 7%);

      .exam-paper-form {
        .label-title {
          display: flex;
          align-items: center;
          justify-content: flex-start;
          font-size: 18px;
          font-weight: 500;
          color: #3b4664;
        }

        .label-tips {
          margin-top: 15px;
          font-size: 15px;
          font-weight: 400;
          color: #3b4664;
        }

        .label-tips2 {
          display: inline-block;
          padding: 5px 10px;
          margin-left: 15px;
          font-size: 12px;
          font-weight: 400;
          color: #e6a23c;
          background-color: #fff6f7;
          border: 1px solid #fbc4c4;
          border-radius: 4px;
        }

        .label {
          display: flex;
          align-items: center;
          justify-content: center;
          margin-right: 20px;
          font-size: 15px;
          font-weight: 400;
          color: #3b4664;

          span {
            color: red;
          }
        }

        .input {
          display: flex;
          flex: 1;
          align-items: center;
          justify-content: flex-start;

          span {
            margin-top: 10px;
            font-size: 13px;
            font-weight: 400;
            color: #8d9295;
            text-align: left;
          }
        }

        .block {
          display: flex;
          flex-direction: column;
          margin-bottom: 20px;

          .input-item {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-top: 10px;
          }
        }
      }

      .footer {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 10px 20px;

        .left {
          display: flex;
          align-items: center;

          .text-btn {
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 16px;
            font-weight: 400;
            cursor: pointer;

            svg {
              font-size: 28px;
            }
          }
        }

        .right {
          display: flex;
          align-items: center;
          justify-content: flex-end;

          .btn {
            width: 96px;
            height: 42px;
            margin-left: 10px;
            border-radius: 10px;

            &:nth-child(1) {
              margin-left: 0;
            }
          }
        }
      }
    }

    .right-content {
      display: flex;
      flex-direction: column;
      width: calc(100% - 500px);
      height: 100%;
      background: #fff;
      border-radius: 8px;
      box-shadow: 0 3px 20px 1px rgb(109 125 126 / 7%);

      .paper-header {
        padding: 20px;
        background: #f8f9fa;
        border-bottom: 1px solid #e9ecef;
        border-radius: 8px 8px 0 0;

        .paper-title {
          margin-bottom: 10px;
          font-size: 18px;
          font-weight: 500;
          color: #333;
        }

        .paper-info {
          display: flex;
          gap: 20px;
          font-size: 14px;
          color: #666;

          span {
            padding: 4px 8px;
            color: #1976d2;
            background: #e3f2fd;
            border-radius: 4px;
          }
        }
      }

      .exam-scrollbar,
      .el-scrollbar__wrap {
        width: 100%;
        // display: flex;
        // flex-direction: column;
        // justify-content: center;
        // align-items: center;
      }

      :deep(.el-scrollbar__view) {
        width: 100%;
        padding: 20px;
      }

      .exam-paper-container {
        width: 100%;

        .empty-state {
          position: absolute;
          top: 50%;
          left: 50%;
          padding: 60px 20px;
          margin: 0 auto;
          color: #999;
          text-align: center;
          transform: translate(-50%, -50%);

          .empty-icon {
            margin-bottom: 16px;
            font-size: 48px;
          }

          .empty-text {
            margin-bottom: 8px;
            font-size: 18px;
            font-weight: 500;
            color: #666;
          }

          .empty-desc {
            font-size: 14px;
            color: #999;
          }
        }

        .selected-questions {
          width: 100%;
          padding: 0 20px;

          .question-drag-container {
            width: 100%;
          }

          .question-item-wrapper {
            width: 100%;
            margin-bottom: 20px;
            background: #fff;

            &:last-child {
              margin-bottom: 0;
            }
          }

          // 拖拽过程中的动画效果
          :deep(.question-item) {
            transition: all 0.3s ease;

            &:hover {
              box-shadow: 0 6px 20px rgb(0 0 0 / 10%);
              transform: translateY(-2px);
            }
          }

          // 拖拽时禁用指针事件
          &.dragging {
            :deep(.question-item) {
              pointer-events: none;
            }
          }
        }
      }
    }
  }
}
</style>
<style lang="scss">
.questionBankDialog {
  padding: 0 !important;

  .el-dialog__header {
    height: 88px;
    text-align: center;
    // background: url("@/assets/images/dialog-header-green2.png") no-repeat;
    // background-size: 100% 100%;

    .dialog-title {
      display: flex;
      align-items: center;
      justify-content: center;
      height: 100%;
      font-size: 24px;
      font-weight: 500;
      // color: #fff;
    }
  }

  .el-dialog__body {
    height: 70vh;
    padding: 0 20px 20px !important;
  }

  .el-dialog__footer {
    padding: 20px !important;
  }

  // .el-dialog__close {
  //   width: 21px;
  //   height: 21px;
  //   background: #fff;
  //   border-radius: 50%;
  //   svg {
  //     font-size: 20px;
  //     path {
  //       fill: #00918c !important;
  //     }
  //   }
  // }

  .dialog-body {
    height: 100%;
  }

  .dialog-content-header {
    display: flex;
    align-items: center;
    margin-bottom: 20px;

    .filter-row {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 260px;
      margin-left: 20px;

      &:nth-child(1) {
        width: 260px;
        margin-left: 0;
      }
    }

    .btn {
      width: 116px;
      height: 40px;
      margin-left: 20px;
    }
  }

  .selection-controls {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 15px 20px;
    margin-bottom: 20px;
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 8px;

    .left-controls {
      display: flex;
      gap: 20px;
      align-items: center;

      .el-checkbox {
        margin-right: 0;

        .el-checkbox__label {
          font-size: 14px;
          font-weight: 500;
          color: #333;
        }
      }

      .el-button {
        padding: 4px 8px;
        font-size: 14px;
        font-weight: 500;

        &:disabled {
          color: #c0c4cc;
          cursor: not-allowed;
        }
      }
    }

    .right-info {
      padding: 6px 12px;
      font-size: 14px;
      font-weight: 500;
      color: #666;
      background: #e3f2fd;
      border-radius: 4px;

      span {
        font-weight: 600;
        color: #1976d2;
      }
    }
  }

  .dialog-content-table {
    height: calc(100% - 180px);
    margin-bottom: 20px;

    .question-list {
      height: 100%;

      .question-item {
        margin-bottom: 15px;

        .question-content {
          display: flex;
          gap: 15px;
          align-items: flex-start;
          padding: 20px;
        }
      }
    }
  }

  .dialog-content-footer {
    display: flex;
    justify-content: center;
    // padding: 10px 0;
  }

  .dialog-footer {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-top: 10px;

    .left {
      display: flex;
      align-items: center;

      span {
        padding: 2px 0;
        font-size: 20px;
        font-weight: 500;
        color: #00918c;
      }
    }

    .right {
      display: flex;
      align-items: center;
    }
  }
}
</style>
