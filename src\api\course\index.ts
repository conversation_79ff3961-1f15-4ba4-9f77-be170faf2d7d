import request from "@/utils/request";
import { AxiosPromise } from "axios";

//分类管理
export function getCategories(queryParams?: any) {
  return request({
    url: "/gkadmin/v1/categories",
    method: "get",
    params: queryParams,
  });
}
export function getCategoriesDetail(id: number, data: any) {
  return request({
    url: "/gkadmin/v1/categories/" + id,
    method: "get",
    data: data,
  });
}

/**
 * 新增
 *
 * @param                                       */
export function addCategories(data: any) {
  return request({
    url: "/gkadmin/v1/categories",
    method: "post",
    data: data,
  });
}

/**
 *  修改
 *
 * @param id
 * @param data
 */
export function updateCategories(id: number, data: any) {
  return request({
    url: "/gkadmin/v1/categories/" + id,
    method: "put",
    data: data,
  });
}

/**
 * 删除
 *
 * @param ids
 */
export function deleteCategories(ids: string) {
  return request({
    url: "/gkadmin/v1/categories/" + ids,
    method: "delete",
  });
}

//课程管理
export function getCourses(queryParams?: any) {
  return request({
    url: "/gkadmin/v1/courses",
    method: "get",
    params: queryParams,
  });
}
export function getCoursesDetail(id: number) {
  return request({
    url: "/gkadmin/v1/courses/" + id,
    method: "get",
    // data: data,
  });
}

/**
 * 新增
 *
 * @param                                    */
export function addCourses(data: any) {
  return request({
    url: "/gkadmin/v1/courses",
    method: "post",
    data: data,
  });
}

/**
 *  修改
 *
 * @param id
 * @param data
 */
export function updateCourses(id: number, data: any) {
  return request({
    url: "/gkadmin/v1/courses/" + id,
    method: "put",
    data: data,
  });
}

/**
 * 删除
 *
 * @param ids
 */
export function deleteCourses(ids: string) {
  return request({
    url: "/gkadmin/v1/courses/" + ids,
    method: "delete",
  });
}

//课程章节管理
export function getCourseChapters(queryParams?: any) {
  return request({
    url: "/gkadmin/v1/course_chapters",
    method: "get",
    params: queryParams,
  });
}
export function getCourseChaptersDetail(id: number, data: any) {
  return request({
    url: "/gkadmin/v1/course_chapters/" + id,
    method: "get",
    data: data,
  });
}

/**
 * 新增
 *
 * @param                                  */
export function addCourseChapters(data: any) {
  return request({
    url: "/gkadmin/v1/course_chapters",
    method: "post",
    data: data,
  });
}

/**
 *  修改
 *
 * @param id
 * @param data
 */
export function updateCourseChapters(id: number, data: any) {
  return request({
    url: "/gkadmin/v1/course_chapters/" + id,
    method: "put",
    data: data,
  });
}

/**
 * 删除
 *
 * @param ids
 */
export function deleteCourseChapters(ids: string) {
  return request({
    url: "/gkadmin/v1/course_chapters/" + ids,
    method: "delete",
  });
}

//课程章节视频管理
export function getCourseResources(queryParams?: any) {
  return request({
    url: "/gkadmin/v1/course_resources",
    method: "get",
    params: queryParams,
  });
}
export function getCourseResourcesDetail(id: number, data: any) {
  return request({
    url: "/gkadmin/v1/course_resources/" + id,
    method: "get",
    data: data,
  });
}

/**
 * 新增
 *
 * @param dat哦·                                       */
export function addCourseResources(data: any) {
  return request({
    url: "/gkadmin/v1/course_resources",
    method: "post",
    data: data,
  });
}

/**
 *  修改
 *
 * @param id
 * @param data
 */
export function updateCourseResources(id: number, data: any) {
  return request({
    url: "/gkadmin/v1/course_resources/" + id,
    method: "put",
    data: data,
  });
}

/**
 * 删除
 *
 * @param ids
 */
export function deleteCourseResources(ids: string) {
  return request({
    url: "/gkadmin/v1/course_resources/" + ids,
    method: "delete",
  });
}

//课程附件管理
export function getAttachments(queryParams?: any) {
  return request({
    url: "/gkadmin/v1/attachments",
    method: "get",
    params: queryParams,
  });
}
export function getAttachmentsDetail(id: number, data: any) {
  return request({
    url: "/gkadmin/v1/attachments/" + id,
    method: "get",
    data: data,
  });
}

/**
 * 新增
 *
 * @param data                                       */
export function addAttachments(data: any) {
  return request({
    url: "/gkadmin/v1/attachments",
    method: "post",
    data: data,
  });
}

/**
 *  修改
 *
 * @param id
 * @param data
 */
export function updateAttachments(id: number, data: any) {
  return request({
    url: "/gkadmin/v1/attachments/" + id,
    method: "put",
    data: data,
  });
}

/**
 * 删除
 *
 * @param ids
 */
export function deleteAttachments(ids: string) {
  return request({
    url: "/gkadmin/v1/attachments/" + ids,
    method: "delete",
  });
}

//免费课程配置
export function getFreeCourses(queryParams?: any) {
  return request({
    url: "/gkadmin/v1/gk_free_courses",
    method: "get",
    params: queryParams,
  });
}

export function addFreeCourses(data: any) {
  return request({
    url: "/gkadmin/v1/gk_free_courses",
    method: "post",
    data: data,
  });
}

export function updateFreeCourses(id: number, data: any) {
  return request({
    url: "/gkadmin/v1/gk_free_courses/" + id,
    method: "put",
    data: data,
  });
}

export function deleteFreeCourses(ids: string) {
  return request({
    url: "/gkadmin/v1/gk_free_courses/" + ids,
    method: "delete",
  });
}

// 平台课程价格管理
export function getDiscountCourse(queryParams?: any) {
  return request({
    url: "/gkadmin/v1/gk_discount_courses",
    method: "get",
    params: queryParams,
  });
}

export function createDiscountCourse(data?: any) {
  return request({
    url: "/gkadmin/v1/gk_discount_courses",
    method: "post",
    data,
  });
}
export function updateDiscountCourse(id: number, data?: any) {
  return request({
    url: "/gkadmin/v1/gk_discount_courses/" + id,
    method: "PUT",
    data,
  });
}
export function deleteDiscountCourse(id: number) {
  return request({
    url: "/gkadmin/v1/gk_discount_courses/" + id,
    method: "delete",
  });
}

//查询课程价格信息
export function getCoursePricing(course_id?: any) {
  return request({
    url: `/gkadmin/v1/courses/${course_id}/pricing`,
    method: "get",
    // params: queryParams,
  });
}

//更新课程价格信息
export function updateCoursePricing(course_id?: any, data?: any) {
  return request({
    url: `/gkadmin/v1/courses/${course_id}/pricing`,
    method: "put",
    data,
  });
}

//获取课程数据销售记录
export function getCourseSales(id?: any, queryParams?: any) {
  return request({
    url: "/gkadmin/v1/courses/" + id + "/sales",
    method: "get",
    params: queryParams,
  });
}
//获取课程数据学习记录
export function getCourseLearnRecords(id?: any, queryParams?: any) {
  return request({
    url: "/gkadmin/v1/courses/" + id + "/learn_records",
    method: "get",
    params: queryParams,
  });
}
//获取课程数据学习记录详情
export function getCourseLearnRecordsDetail(id?: any, recordId?: any) {
  return request({
    url: "/gkadmin/v1/courses/" + id + "/learn_records/" + recordId,
    method: "get",
  });
}
//获取课程数据学习记录中的考试记录
export function getCourseExamRecordsById(id?: any, queryParams?: any) {
  return request({
    url: "/gkadmin/v1/courses/" + id + "/user_exams",
    method: "get",
    params: queryParams,
  });
}
//获取课程数据学习记录中的考试记录详情
export function getExamRecordDetail(
  id?: any,
  exam_id?: any,
  queryParams?: any
) {
  return request({
    url: "/gkadmin/v1/courses/" + id + "/user_exams/" + exam_id,
    method: "get",
    params: queryParams,
  });
}
