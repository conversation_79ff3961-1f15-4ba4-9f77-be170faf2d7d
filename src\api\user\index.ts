import request from "@/utils/request";
import { AxiosPromise } from "axios";

/**
 * 登录成功后获取用户信息（昵称、头像、权限集合和角色集合）
 */
// export function getUserInfoApi(): AxiosPromise<UserInfo> {
//   return request({
//     url: "/api/v1/users/me",
//     method: "get",
//   });
// }

// /**
//  * 获取用户分页列表
//  *
//  * @param queryParams
//  */
// export function getUserPage(
//   queryParams: UserQuery
// ): AxiosPromise<PageResult<UserPageVO[]>> {
//   return request({
//     url: "/api/v1/users/page",
//     method: "get",
//     params: queryParams,
//   });
// }

// /**
//  * 获取用户表单详情
//  *
//  * @param userId
//  */
// export function getUserForm(userId: number): AxiosPromise<UserForm> {
//   return request({
//     url: "/api/v1/users/" + userId + "/form",
//     method: "get",
//   });
// }

// /**
//  * 添加用户
//  *
//  * @param data
//  */
// export function addUser(data: any) {
//   return request({
//     url: "/api/v1/users",
//     method: "post",
//     data: data,
//   });
// }

// /**
//  * 修改用户
//  *
//  * @param id
//  * @param data
//  */
// export function updateUser(id: number, data: UserForm) {
//   return request({
//     url: "/api/v1/users/" + id,
//     method: "put",
//     data: data,
//   });
// }

// /**
//  * 修改用户密码
//  *
//  * @param id
//  * @param password
//  */
// export function updateUserPassword(id: number, password: string) {
//   return request({
//     url: "/api/v1/users/" + id + "/password",
//     method: "patch",
//     params: { password: password },
//   });
// }

// /**
//  * 删除用户
//  *
//  * @param ids
//  */
// export function deleteUsers(ids: string) {
//   return request({
//     url: "/api/v1/users/" + ids,
//     method: "delete",
//   });
// }

// /**
//  * 下载用户导入模板
//  *
//  * @returns
//  */
// export function downloadTemplateApi() {
//   return request({
//     url: "/api/v1/users/template",
//     method: "get",
//     responseType: "arraybuffer",
//   });
// }

// /**
//  * 导出用户
//  *
//  * @param queryParams
//  * @returns
//  */
// export function exportUser(queryParams: UserQuery) {
//   return request({
//     url: "/api/v1/users/_export",
//     method: "get",
//     params: queryParams,
//     responseType: "arraybuffer",
//   });
// }

// /**
//  * 导入用户
//  *
//  * @param file
//  */
// export function importUser(deptId: number, file: File) {
//   const formData = new FormData();
//   formData.append("file", file);
//   return request({
//     url: "/api/v1/users/_import",
//     method: "post",
//     params: { deptId: deptId },
//     data: formData,
//     headers: {
//       "Content-Type": "multipart/form-data",
//     },
//   });
// }

//以上为mock

// 获取用户列表
export function getUsers(queryParams?: any) {
  return request({
    url: "/gkadmin/v1/users",
    method: "get",
    params: queryParams,
  });
}
// 冻结，解冻用户
export function updateUsers(id: number, data: any) {
  return request({
    url: "/gkadmin/v1/users/" + id,
    method: "put",
    data: data,
  });
}

//web 退出登录
export function userLogout(id: any) {
  return request({
    url: "/gkadmin/v1/sessions/" + id,
    method: "delete",
  });
}

/**
 * 登录
 */
export function userLogin(data: any) {
  return request({
    url: "/gkadmin/v1/sessions",
    method: "POST",
    data,
  });
}
/**
 * 获取手机验证码
 */
export function getVerifyCode(data: any) {
  return request({
    url: "gkadmin/v1/verify_codes",
    method: "POST",
    data,
  });
}

/**
 * 个人信息TODO:
 */
export function getMyInfo() {
  return request({
    url: "/gkadmin/v1/my_info",
    method: "get",
  });
}

/**
 * 绿豆赠送:
 */
export function giveGBean(data: any) {
  return request({
    url: "/gkadmin/v1/gb_bestowals",
    method: "post",
    data,
  });
}

/**
 * 课程、vip赠送:
 */
export function giveOrder(data: any) {
  return request({
    url: "gkadmin/v1/bestowal_orders",
    method: "post",
    data,
  });
}
